import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import {
  Venue, Section, Row, Seat, Point, Size,
  SeatStatus, SeatCategory, SectionType, DesignTool,
  ToolConfig, ViewportState, SelectionState, VenueStats,
  VenueEvent, VenueEventType, RowNaming, VirtualizationConfig,
  LODLevel, ExportOptions, ExportFormat, ImportResult,
  ContextMenuState, ContextMenuAction
} from '../models/venue.models';

@Injectable({
  providedIn: 'root'
})
export class VenueService {
  private venueSubject = new BehaviorSubject<Venue | null>(null);
  private toolConfigSubject = new BehaviorSubject<ToolConfig>(this.getDefaultToolConfig());
  private viewportSubject = new BehaviorSubject<ViewportState>(this.getDefaultViewport());
  private selectionSubject = new BehaviorSubject<SelectionState>(this.getDefaultSelection());
  private contextMenuSubject = new BehaviorSubject<ContextMenuState>(this.getDefaultContextMenu());
  private eventsSubject = new Subject<VenueEvent>();

  public venue$ = this.venueSubject.asObservable();
  public toolConfig$ = this.toolConfigSubject.asObservable();
  public viewport$ = this.viewportSubject.asObservable();
  public selection$ = this.selectionSubject.asObservable();
  public contextMenu$ = this.contextMenuSubject.asObservable();
  public events$ = this.eventsSubject.asObservable();

  constructor() {}

  // Venue Management
  createVenue(name: string, canvasSize: Size): Venue {
    const venue: Venue = {
      id: uuidv4(),
      name,
      sections: [],
      totalCapacity: 0,
      canvasSize,
      metadata: {
        created: new Date(),
        modified: new Date(),
        version: '1.0.0',
        author: 'Venue Designer'
      }
    };
    
    this.venueSubject.next(venue);
    this.emitEvent(VenueEventType.SECTION_CREATED, venue);
    return venue;
  }

  loadVenue(venue: Venue): void {
    this.venueSubject.next(venue);
    this.updateTotalCapacity();
  }

  getCurrentVenue(): Venue | null {
    return this.venueSubject.value;
  }

  // Section Management
  createSection(type: SectionType, name: string, position: Point, size: Size, color: string): Section {
    const section: Section = {
      id: uuidv4(),
      name,
      type,
      position,
      size,
      rows: [],
      color,
      capacity: 0,
      isVisible: true,
      rotation: 0
    };

    const venue = this.getCurrentVenue();
    if (venue) {
      venue.sections.push(section);
      this.updateVenue(venue);
      this.emitEvent(VenueEventType.SECTION_CREATED, section);
    }

    return section;
  }

  updateSection(sectionId: string, updates: Partial<Section>): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    const sectionIndex = venue.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex !== -1) {
      venue.sections[sectionIndex] = { ...venue.sections[sectionIndex], ...updates };
      this.updateVenue(venue);
      this.emitEvent(VenueEventType.SECTION_MODIFIED, venue.sections[sectionIndex]);
    }
  }

  deleteSection(sectionId: string): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    venue.sections = venue.sections.filter(s => s.id !== sectionId);
    this.updateVenue(venue);
    this.emitEvent(VenueEventType.SECTION_DELETED, { sectionId });
  }

  // Row Management
  createRow(sectionId: string, name: string, position: Point, seatsCount: number, 
           seatSpacing: number = 30, curve: number = 0): Row {
    const row: Row = {
      id: uuidv4(),
      name,
      seats: [],
      position,
      angle: 0,
      curve,
      sectionId
    };

    // Create seats for the row
    for (let i = 0; i < seatsCount; i++) {
      const seat = this.createSeat(
        row.id,
        name,
        (i + 1).toString(),
        {
          x: position.x + (i * seatSpacing),
          y: position.y
        }
      );
      row.seats.push(seat);
    }

    const venue = this.getCurrentVenue();
    if (venue) {
      const section = venue.sections.find(s => s.id === sectionId);
      if (section) {
        section.rows.push(row);
        this.updateSectionCapacity(section);
        this.updateVenue(venue);
        this.emitEvent(VenueEventType.ROW_CREATED, row);
      }
    }

    return row;
  }

  createMultipleRows(sectionId: string, startPosition: Point, rowCount: number, 
                    seatsPerRow: number, rowSpacing: number = 40, seatSpacing: number = 30,
                    rowNaming: RowNaming = RowNaming.ALPHABETIC, curve: number = 0): Row[] {
    const rows: Row[] = [];
    
    for (let i = 0; i < rowCount; i++) {
      const rowName = this.generateRowName(i, rowNaming);
      const rowPosition: Point = {
        x: startPosition.x,
        y: startPosition.y + (i * rowSpacing)
      };
      
      const row = this.createRow(sectionId, rowName, rowPosition, seatsPerRow, seatSpacing, curve);
      rows.push(row);
    }
    
    return rows;
  }

  // Seat Management
  createSeat(rowId: string, rowName: string, seatNumber: string, position: Point): Seat {
    return {
      id: uuidv4(),
      row: rowName,
      number: seatNumber,
      position,
      status: SeatStatus.AVAILABLE,
      category: SeatCategory.STANDARD
    };
  }

  updateSeatStatus(seatId: string, status: SeatStatus): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    for (const section of venue.sections) {
      for (const row of section.rows) {
        const seat = row.seats.find(s => s.id === seatId);
        if (seat) {
          seat.status = status;
          this.updateVenue(venue);
          
          if (status === SeatStatus.SELECTED) {
            this.emitEvent(VenueEventType.SEAT_SELECTED, seat);
          } else if (status === SeatStatus.AVAILABLE) {
            this.emitEvent(VenueEventType.SEAT_DESELECTED, seat);
          }
          return;
        }
      }
    }
  }

  // Selection Management
  selectSeat(seatId: string, multiSelect: boolean = false): void {
    const selection = this.selectionSubject.value;

    if (!multiSelect) {
      // Clear previous selections if not multi-selecting
      this.clearSelection();
    }

    if (!selection.selectedSeats.includes(seatId)) {
      selection.selectedSeats.push(seatId);
      selection.multiSelect = multiSelect;
      this.selectionSubject.next(selection);
      this.updateSeatStatus(seatId, SeatStatus.SELECTED);
    }
  }

  selectSection(sectionId: string, multiSelect: boolean = false): void {
    const selection = this.selectionSubject.value;

    if (!multiSelect) {
      this.clearSelection();
    }

    if (!selection.selectedSections.includes(sectionId)) {
      selection.selectedSections.push(sectionId);
      selection.multiSelect = multiSelect;
      this.selectionSubject.next(selection);
    }
  }

  selectRow(rowId: string, multiSelect: boolean = false): void {
    const selection = this.selectionSubject.value;

    if (!multiSelect) {
      this.clearSelection();
    }

    if (!selection.selectedRows.includes(rowId)) {
      selection.selectedRows.push(rowId);
      selection.multiSelect = multiSelect;
      this.selectionSubject.next(selection);
    }
  }

  deselectSeat(seatId: string): void {
    const selection = this.selectionSubject.value;
    selection.selectedSeats = selection.selectedSeats.filter(id => id !== seatId);
    this.selectionSubject.next(selection);
    this.updateSeatStatus(seatId, SeatStatus.AVAILABLE);
  }

  deselectSection(sectionId: string): void {
    const selection = this.selectionSubject.value;
    selection.selectedSections = selection.selectedSections.filter(id => id !== sectionId);
    this.selectionSubject.next(selection);
  }

  deselectRow(rowId: string): void {
    const selection = this.selectionSubject.value;
    selection.selectedRows = selection.selectedRows.filter(id => id !== rowId);
    this.selectionSubject.next(selection);
  }

  clearSelection(): void {
    const selection = this.selectionSubject.value;

    // Reset all selected seats to available
    selection.selectedSeats.forEach(seatId => {
      this.updateSeatStatus(seatId, SeatStatus.AVAILABLE);
    });

    this.selectionSubject.next(this.getDefaultSelection());
  }

  // Context Menu Management
  showContextMenu(x: number, y: number, targetType: 'seat' | 'row' | 'section' | 'canvas', targetId: string | null = null): void {
    const actions = this.getContextMenuActions(targetType, targetId);

    const contextMenu: ContextMenuState = {
      visible: true,
      x,
      y,
      targetType,
      targetId,
      actions
    };

    this.contextMenuSubject.next(contextMenu);
  }

  hideContextMenu(): void {
    this.contextMenuSubject.next(this.getDefaultContextMenu());
  }

  private getContextMenuActions(targetType: string, targetId: string | null): ContextMenuAction[] {
    const actions: ContextMenuAction[] = [];

    switch (targetType) {
      case 'seat':
        actions.push(
          {
            id: 'select-seat',
            label: 'Select Seat',
            icon: 'fas fa-check',
            action: () => targetId && this.selectSeat(targetId)
          },
          {
            id: 'delete-seat',
            label: 'Delete Seat',
            icon: 'fas fa-trash',
            action: () => targetId && this.deleteSeat(targetId)
          },
          { id: 'sep1', label: '', icon: '', action: () => {}, separator: true },
          {
            id: 'seat-properties',
            label: 'Properties',
            icon: 'fas fa-cog',
            action: () => this.showSeatProperties(targetId)
          }
        );
        break;

      case 'row':
        actions.push(
          {
            id: 'select-row',
            label: 'Select Row',
            icon: 'fas fa-check',
            action: () => targetId && this.selectRow(targetId)
          },
          {
            id: 'delete-row',
            label: 'Delete Row',
            icon: 'fas fa-trash',
            action: () => targetId && this.deleteRow(targetId)
          },
          { id: 'sep1', label: '', icon: '', action: () => {}, separator: true },
          {
            id: 'add-seats',
            label: 'Add Seats',
            icon: 'fas fa-plus',
            action: () => this.addSeatsToRow(targetId)
          }
        );
        break;

      case 'section':
        actions.push(
          {
            id: 'select-section',
            label: 'Select Section',
            icon: 'fas fa-check',
            action: () => targetId && this.selectSection(targetId)
          },
          {
            id: 'delete-section',
            label: 'Delete Section',
            icon: 'fas fa-trash',
            action: () => targetId && this.deleteSection(targetId)
          },
          { id: 'sep1', label: '', icon: '', action: () => {}, separator: true },
          {
            id: 'add-row',
            label: 'Add Row',
            icon: 'fas fa-plus',
            action: () => this.addRowToSection(targetId)
          },
          {
            id: 'section-properties',
            label: 'Properties',
            icon: 'fas fa-cog',
            action: () => this.showSectionProperties(targetId)
          }
        );
        break;

      case 'canvas':
        actions.push(
          {
            id: 'add-section',
            label: 'Add Section',
            icon: 'fas fa-plus',
            action: () => this.createSectionAtPosition({ x: this.contextMenuSubject.value.x, y: this.contextMenuSubject.value.y })
          },
          { id: 'sep1', label: '', icon: '', action: () => {}, separator: true },
          {
            id: 'clear-selection',
            label: 'Clear Selection',
            icon: 'fas fa-times',
            action: () => this.clearSelection()
          },
          {
            id: 'export-venue',
            label: 'Export Venue',
            icon: 'fas fa-download',
            action: () => this.exportVenueToJson()
          }
        );
        break;
    }

    return actions;
  }

  // Tool Management
  setSelectedTool(tool: DesignTool): void {
    const config = this.toolConfigSubject.value;
    config.selectedTool = tool;
    this.toolConfigSubject.next(config);
    this.emitEvent(VenueEventType.TOOL_CHANGED, tool);
  }

  updateToolConfig(updates: Partial<ToolConfig>): void {
    const config = { ...this.toolConfigSubject.value, ...updates };
    this.toolConfigSubject.next(config);
  }

  // Viewport Management
  updateViewport(updates: Partial<ViewportState>): void {
    const viewport = { ...this.viewportSubject.value, ...updates };
    this.viewportSubject.next(viewport);
    this.emitEvent(VenueEventType.VIEWPORT_CHANGED, viewport);
  }

  zoomIn(factor: number = 1.2): void {
    const viewport = this.viewportSubject.value;
    const newScale = Math.min(viewport.scale * factor, viewport.maxScale);
    this.updateViewport({ scale: newScale });
  }

  zoomOut(factor: number = 0.8): void {
    const viewport = this.viewportSubject.value;
    const newScale = Math.max(viewport.scale * factor, viewport.minScale);
    this.updateViewport({ scale: newScale });
  }

  resetZoom(): void {
    this.updateViewport({ scale: 1, position: { x: 0, y: 0 } });
  }

  // Statistics
  getVenueStats(): VenueStats {
    const venue = this.getCurrentVenue();
    if (!venue) {
      return {
        totalSeats: 0,
        availableSeats: 0,
        selectedSeats: 0,
        occupiedSeats: 0,
        sectionStats: []
      };
    }

    let totalSeats = 0;
    let availableSeats = 0;
    let selectedSeats = 0;
    let occupiedSeats = 0;
    const sectionStats = [];

    for (const section of venue.sections) {
      let sectionTotal = 0;
      let sectionAvailable = 0;
      let sectionSelected = 0;
      let sectionOccupied = 0;

      for (const row of section.rows) {
        for (const seat of row.seats) {
          sectionTotal++;
          totalSeats++;

          switch (seat.status) {
            case SeatStatus.AVAILABLE:
              sectionAvailable++;
              availableSeats++;
              break;
            case SeatStatus.SELECTED:
              sectionSelected++;
              selectedSeats++;
              break;
            case SeatStatus.OCCUPIED:
              sectionOccupied++;
              occupiedSeats++;
              break;
          }
        }
      }

      sectionStats.push({
        sectionId: section.id,
        sectionName: section.name,
        totalSeats: sectionTotal,
        availableSeats: sectionAvailable,
        selectedSeats: sectionSelected,
        occupiedSeats: sectionOccupied
      });
    }

    return {
      totalSeats,
      availableSeats,
      selectedSeats,
      occupiedSeats,
      sectionStats
    };
  }

  // Utility Methods
  private updateVenue(venue: Venue): void {
    venue.metadata.modified = new Date();
    this.updateTotalCapacity();
    this.venueSubject.next(venue);
  }

  private updateTotalCapacity(): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    venue.totalCapacity = venue.sections.reduce((total, section) => {
      return total + section.rows.reduce((sectionTotal, row) => {
        return sectionTotal + row.seats.length;
      }, 0);
    }, 0);
  }

  private updateSectionCapacity(section: Section): void {
    section.capacity = section.rows.reduce((total, row) => total + row.seats.length, 0);
  }

  private generateRowName(index: number, naming: RowNaming): string {
    switch (naming) {
      case RowNaming.ALPHABETIC:
        return String.fromCharCode(65 + index); // A, B, C...
      case RowNaming.NUMERIC:
        return (index + 1).toString(); // 1, 2, 3...
      case RowNaming.ROMAN:
        return this.toRoman(index + 1); // I, II, III...
      default:
        return `Row ${index + 1}`;
    }
  }

  private toRoman(num: number): string {
    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
    let result = '';
    
    for (let i = 0; i < values.length; i++) {
      while (num >= values[i]) {
        result += symbols[i];
        num -= values[i];
      }
    }
    
    return result;
  }

  private emitEvent(type: VenueEventType, data: any): void {
    this.eventsSubject.next({
      type,
      data,
      timestamp: new Date()
    });
  }

  private getDefaultToolConfig(): ToolConfig {
    return {
      selectedTool: DesignTool.SELECT,
      sectionConfig: {
        type: SectionType.ORCHESTRA,
        name: 'New Section',
        color: '#4CAF50',
        width: 300,
        height: 200
      },
      rowConfig: {
        seatsPerRow: 20,
        rowSpacing: 40,
        seatSpacing: 30,
        startNumber: 1,
        rowNaming: RowNaming.ALPHABETIC,
        curve: 0
      },
      seatConfig: {
        category: SeatCategory.STANDARD,
        price: 50,
        size: 20,
        isAccessible: false
      }
    };
  }

  private getDefaultViewport(): ViewportState {
    return {
      scale: 1,
      position: { x: 0, y: 0 },
      minScale: 0.1,
      maxScale: 5
    };
  }

  // Delete Operations
  deleteSeat(seatId: string): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    for (const section of venue.sections) {
      for (const row of section.rows) {
        const seatIndex = row.seats.findIndex(s => s.id === seatId);
        if (seatIndex !== -1) {
          row.seats.splice(seatIndex, 1);
          this.updateVenue(venue);
          this.deselectSeat(seatId);
          return;
        }
      }
    }
  }

  deleteRow(rowId: string): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    for (const section of venue.sections) {
      const rowIndex = section.rows.findIndex(r => r.id === rowId);
      if (rowIndex !== -1) {
        // Remove all seats from selection
        section.rows[rowIndex].seats.forEach(seat => {
          this.deselectSeat(seat.id);
        });

        section.rows.splice(rowIndex, 1);
        this.updateSectionCapacity(section);
        this.updateVenue(venue);
        this.deselectRow(rowId);
        this.emitEvent(VenueEventType.ROW_DELETED, { rowId });
        return;
      }
    }
  }

  // Export/Import Operations
  exportVenueToJson(): void {
    const venue = this.getCurrentVenue();
    if (!venue) {
      console.warn('No venue to export');
      return;
    }

    const exportData = {
      venue: venue,
      exportedAt: new Date().toISOString(),
      version: '1.0.0',
      stats: this.getVenueStats()
    };

    console.log('🎭 Venue Export Data:', JSON.stringify(exportData, null, 2));

    // Also trigger download
    this.downloadJsonFile(exportData, `venue-${venue.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`);
  }

  private downloadJsonFile(data: any, filename: string): void {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // Helper methods for context menu actions
  private createSectionAtPosition(position: Point): void {
    const config = this.toolConfigSubject.value.sectionConfig;
    this.createSection(
      config.type,
      config.name,
      position,
      { width: config.width, height: config.height },
      config.color
    );
    this.hideContextMenu();
  }

  private showSeatProperties(seatId: string | null): void {
    console.log('Show seat properties for:', seatId);
    this.hideContextMenu();
  }

  private showSectionProperties(sectionId: string | null): void {
    console.log('Show section properties for:', sectionId);
    this.hideContextMenu();
  }

  private addRowToSection(sectionId: string | null): void {
    if (!sectionId) return;

    const venue = this.getCurrentVenue();
    const section = venue?.sections.find(s => s.id === sectionId);
    if (section) {
      const config = this.toolConfigSubject.value.rowConfig;
      const yPosition = section.position.y + 50 + (section.rows.length * config.rowSpacing);

      this.createRow(
        sectionId,
        this.generateRowName(section.rows.length, config.rowNaming),
        { x: section.position.x + 50, y: yPosition },
        config.seatsPerRow,
        config.seatSpacing
      );
    }
    this.hideContextMenu();
  }

  private addSeatsToRow(rowId: string | null): void {
    console.log('Add seats to row:', rowId);
    this.hideContextMenu();
  }

  private getDefaultSelection(): SelectionState {
    return {
      selectedSeats: [],
      selectedSections: [],
      selectedRows: [],
      multiSelect: false
    };
  }

  private getDefaultContextMenu(): ContextMenuState {
    return {
      visible: false,
      x: 0,
      y: 0,
      targetType: null,
      targetId: null,
      actions: []
    };
  }
}
