import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import {
  Venue, Section, Row, Seat, Point, Size,
  SeatStatus, SeatCategory, SectionType, DesignTool,
  ToolConfig, ViewportState, SelectionState, VenueStats,
  VenueEvent, VenueEventType, RowNaming, VirtualizationConfig,
  LODLevel, ExportOptions, ExportFormat, ImportResult
} from '../models/venue.models';

@Injectable({
  providedIn: 'root'
})
export class VenueService {
  private venueSubject = new BehaviorSubject<Venue | null>(null);
  private toolConfigSubject = new BehaviorSubject<ToolConfig>(this.getDefaultToolConfig());
  private viewportSubject = new BehaviorSubject<ViewportState>(this.getDefaultViewport());
  private selectionSubject = new BehaviorSubject<SelectionState>(this.getDefaultSelection());
  private eventsSubject = new Subject<VenueEvent>();

  public venue$ = this.venueSubject.asObservable();
  public toolConfig$ = this.toolConfigSubject.asObservable();
  public viewport$ = this.viewportSubject.asObservable();
  public selection$ = this.selectionSubject.asObservable();
  public events$ = this.eventsSubject.asObservable();

  constructor() {}

  // Venue Management
  createVenue(name: string, canvasSize: Size): Venue {
    const venue: Venue = {
      id: uuidv4(),
      name,
      sections: [],
      totalCapacity: 0,
      canvasSize,
      metadata: {
        created: new Date(),
        modified: new Date(),
        version: '1.0.0',
        author: 'Venue Designer'
      }
    };
    
    this.venueSubject.next(venue);
    this.emitEvent(VenueEventType.SECTION_CREATED, venue);
    return venue;
  }

  loadVenue(venue: Venue): void {
    this.venueSubject.next(venue);
    this.updateTotalCapacity();
  }

  getCurrentVenue(): Venue | null {
    return this.venueSubject.value;
  }

  // Section Management
  createSection(type: SectionType, name: string, position: Point, size: Size, color: string): Section {
    const section: Section = {
      id: uuidv4(),
      name,
      type,
      position,
      size,
      rows: [],
      color,
      capacity: 0,
      isVisible: true,
      rotation: 0
    };

    const venue = this.getCurrentVenue();
    if (venue) {
      venue.sections.push(section);
      this.updateVenue(venue);
      this.emitEvent(VenueEventType.SECTION_CREATED, section);
    }

    return section;
  }

  updateSection(sectionId: string, updates: Partial<Section>): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    const sectionIndex = venue.sections.findIndex(s => s.id === sectionId);
    if (sectionIndex !== -1) {
      venue.sections[sectionIndex] = { ...venue.sections[sectionIndex], ...updates };
      this.updateVenue(venue);
      this.emitEvent(VenueEventType.SECTION_MODIFIED, venue.sections[sectionIndex]);
    }
  }

  deleteSection(sectionId: string): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    venue.sections = venue.sections.filter(s => s.id !== sectionId);
    this.updateVenue(venue);
    this.emitEvent(VenueEventType.SECTION_DELETED, { sectionId });
  }

  // Row Management
  createRow(sectionId: string, name: string, position: Point, seatsCount: number, 
           seatSpacing: number = 30, curve: number = 0): Row {
    const row: Row = {
      id: uuidv4(),
      name,
      seats: [],
      position,
      angle: 0,
      curve,
      sectionId
    };

    // Create seats for the row
    for (let i = 0; i < seatsCount; i++) {
      const seat = this.createSeat(
        row.id,
        name,
        (i + 1).toString(),
        {
          x: position.x + (i * seatSpacing),
          y: position.y
        }
      );
      row.seats.push(seat);
    }

    const venue = this.getCurrentVenue();
    if (venue) {
      const section = venue.sections.find(s => s.id === sectionId);
      if (section) {
        section.rows.push(row);
        this.updateSectionCapacity(section);
        this.updateVenue(venue);
        this.emitEvent(VenueEventType.ROW_CREATED, row);
      }
    }

    return row;
  }

  createMultipleRows(sectionId: string, startPosition: Point, rowCount: number, 
                    seatsPerRow: number, rowSpacing: number = 40, seatSpacing: number = 30,
                    rowNaming: RowNaming = RowNaming.ALPHABETIC, curve: number = 0): Row[] {
    const rows: Row[] = [];
    
    for (let i = 0; i < rowCount; i++) {
      const rowName = this.generateRowName(i, rowNaming);
      const rowPosition: Point = {
        x: startPosition.x,
        y: startPosition.y + (i * rowSpacing)
      };
      
      const row = this.createRow(sectionId, rowName, rowPosition, seatsPerRow, seatSpacing, curve);
      rows.push(row);
    }
    
    return rows;
  }

  // Seat Management
  createSeat(rowId: string, rowName: string, seatNumber: string, position: Point): Seat {
    return {
      id: uuidv4(),
      row: rowName,
      number: seatNumber,
      position,
      status: SeatStatus.AVAILABLE,
      category: SeatCategory.STANDARD
    };
  }

  updateSeatStatus(seatId: string, status: SeatStatus): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    for (const section of venue.sections) {
      for (const row of section.rows) {
        const seat = row.seats.find(s => s.id === seatId);
        if (seat) {
          seat.status = status;
          this.updateVenue(venue);
          
          if (status === SeatStatus.SELECTED) {
            this.emitEvent(VenueEventType.SEAT_SELECTED, seat);
          } else if (status === SeatStatus.AVAILABLE) {
            this.emitEvent(VenueEventType.SEAT_DESELECTED, seat);
          }
          return;
        }
      }
    }
  }

  // Selection Management
  selectSeat(seatId: string): void {
    const selection = this.selectionSubject.value;
    if (!selection.selectedSeats.includes(seatId)) {
      selection.selectedSeats.push(seatId);
      this.selectionSubject.next(selection);
      this.updateSeatStatus(seatId, SeatStatus.SELECTED);
    }
  }

  deselectSeat(seatId: string): void {
    const selection = this.selectionSubject.value;
    selection.selectedSeats = selection.selectedSeats.filter(id => id !== seatId);
    this.selectionSubject.next(selection);
    this.updateSeatStatus(seatId, SeatStatus.AVAILABLE);
  }

  clearSelection(): void {
    const selection = this.selectionSubject.value;
    
    // Reset all selected seats to available
    selection.selectedSeats.forEach(seatId => {
      this.updateSeatStatus(seatId, SeatStatus.AVAILABLE);
    });
    
    this.selectionSubject.next(this.getDefaultSelection());
  }

  // Tool Management
  setSelectedTool(tool: DesignTool): void {
    const config = this.toolConfigSubject.value;
    config.selectedTool = tool;
    this.toolConfigSubject.next(config);
    this.emitEvent(VenueEventType.TOOL_CHANGED, tool);
  }

  updateToolConfig(updates: Partial<ToolConfig>): void {
    const config = { ...this.toolConfigSubject.value, ...updates };
    this.toolConfigSubject.next(config);
  }

  // Viewport Management
  updateViewport(updates: Partial<ViewportState>): void {
    const viewport = { ...this.viewportSubject.value, ...updates };
    this.viewportSubject.next(viewport);
    this.emitEvent(VenueEventType.VIEWPORT_CHANGED, viewport);
  }

  zoomIn(factor: number = 1.2): void {
    const viewport = this.viewportSubject.value;
    const newScale = Math.min(viewport.scale * factor, viewport.maxScale);
    this.updateViewport({ scale: newScale });
  }

  zoomOut(factor: number = 0.8): void {
    const viewport = this.viewportSubject.value;
    const newScale = Math.max(viewport.scale * factor, viewport.minScale);
    this.updateViewport({ scale: newScale });
  }

  resetZoom(): void {
    this.updateViewport({ scale: 1, position: { x: 0, y: 0 } });
  }

  // Statistics
  getVenueStats(): VenueStats {
    const venue = this.getCurrentVenue();
    if (!venue) {
      return {
        totalSeats: 0,
        availableSeats: 0,
        selectedSeats: 0,
        occupiedSeats: 0,
        sectionStats: []
      };
    }

    let totalSeats = 0;
    let availableSeats = 0;
    let selectedSeats = 0;
    let occupiedSeats = 0;
    const sectionStats = [];

    for (const section of venue.sections) {
      let sectionTotal = 0;
      let sectionAvailable = 0;
      let sectionSelected = 0;
      let sectionOccupied = 0;

      for (const row of section.rows) {
        for (const seat of row.seats) {
          sectionTotal++;
          totalSeats++;

          switch (seat.status) {
            case SeatStatus.AVAILABLE:
              sectionAvailable++;
              availableSeats++;
              break;
            case SeatStatus.SELECTED:
              sectionSelected++;
              selectedSeats++;
              break;
            case SeatStatus.OCCUPIED:
              sectionOccupied++;
              occupiedSeats++;
              break;
          }
        }
      }

      sectionStats.push({
        sectionId: section.id,
        sectionName: section.name,
        totalSeats: sectionTotal,
        availableSeats: sectionAvailable,
        selectedSeats: sectionSelected,
        occupiedSeats: sectionOccupied
      });
    }

    return {
      totalSeats,
      availableSeats,
      selectedSeats,
      occupiedSeats,
      sectionStats
    };
  }

  // Utility Methods
  private updateVenue(venue: Venue): void {
    venue.metadata.modified = new Date();
    this.updateTotalCapacity();
    this.venueSubject.next(venue);
  }

  private updateTotalCapacity(): void {
    const venue = this.getCurrentVenue();
    if (!venue) return;

    venue.totalCapacity = venue.sections.reduce((total, section) => {
      return total + section.rows.reduce((sectionTotal, row) => {
        return sectionTotal + row.seats.length;
      }, 0);
    }, 0);
  }

  private updateSectionCapacity(section: Section): void {
    section.capacity = section.rows.reduce((total, row) => total + row.seats.length, 0);
  }

  private generateRowName(index: number, naming: RowNaming): string {
    switch (naming) {
      case RowNaming.ALPHABETIC:
        return String.fromCharCode(65 + index); // A, B, C...
      case RowNaming.NUMERIC:
        return (index + 1).toString(); // 1, 2, 3...
      case RowNaming.ROMAN:
        return this.toRoman(index + 1); // I, II, III...
      default:
        return `Row ${index + 1}`;
    }
  }

  private toRoman(num: number): string {
    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];
    let result = '';
    
    for (let i = 0; i < values.length; i++) {
      while (num >= values[i]) {
        result += symbols[i];
        num -= values[i];
      }
    }
    
    return result;
  }

  private emitEvent(type: VenueEventType, data: any): void {
    this.eventsSubject.next({
      type,
      data,
      timestamp: new Date()
    });
  }

  private getDefaultToolConfig(): ToolConfig {
    return {
      selectedTool: DesignTool.SELECT,
      sectionConfig: {
        type: SectionType.ORCHESTRA,
        name: 'New Section',
        color: '#4CAF50',
        width: 300,
        height: 200
      },
      rowConfig: {
        seatsPerRow: 20,
        rowSpacing: 40,
        seatSpacing: 30,
        startNumber: 1,
        rowNaming: RowNaming.ALPHABETIC,
        curve: 0
      },
      seatConfig: {
        category: SeatCategory.STANDARD,
        price: 50,
        size: 20,
        isAccessible: false
      }
    };
  }

  private getDefaultViewport(): ViewportState {
    return {
      scale: 1,
      position: { x: 0, y: 0 },
      minScale: 0.1,
      maxScale: 5
    };
  }

  private getDefaultSelection(): SelectionState {
    return {
      selectedSeats: [],
      selectedSections: [],
      selectedRows: []
    };
  }
}
