import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import Konva from 'konva';
import {
  Venue, Section, Row, Seat, Point, ViewportState,
  VirtualizationConfig, LODLevel
} from '../models/venue.models';

@Injectable({
  providedIn: 'root'
})
export class VenuePerformanceService {
  private virtualizationConfig: VirtualizationConfig = {
    enabled: true,
    chunkSize: 1000, // Render seats in chunks of 1000
    renderDistance: 2000, // Only render objects within 2000px of viewport
    lodLevels: [
      {
        scale: 0.1,
        renderSeats: false,
        renderSeatNumbers: false,
        renderRowLabels: false,
        simplifyShapes: true
      },
      {
        scale: 0.3,
        renderSeats: true,
        renderSeatNumbers: false,
        renderRowLabels: true,
        simplifyShapes: true
      },
      {
        scale: 0.7,
        renderSeats: true,
        renderSeatNumbers: true,
        renderRowLabels: true,
        simplifyShapes: false
      },
      {
        scale: 1.0,
        renderSeats: true,
        renderSeatNumbers: true,
        renderRowLabels: true,
        simplifyShapes: false
      }
    ]
  };

  private renderQueue: (() => void)[] = [];
  private isProcessingQueue = false;
  private frameId: number | null = null;

  constructor() {}

  /**
   * Optimizes venue rendering for large venues (150k+ seats)
   */
  optimizeVenueRendering(
    venue: Venue,
    viewport: ViewportState,
    stage: Konva.Stage,
    layer: Konva.Layer,
    seatLayer: Konva.Layer
  ): void {
    if (!this.virtualizationConfig.enabled) {
      this.renderAllSeats(venue, layer, seatLayer);
      return;
    }

    const currentLOD = this.getCurrentLODLevel(viewport.scale);
    const visibleBounds = this.getVisibleBounds(viewport, stage);
    
    this.renderWithVirtualization(venue, currentLOD, visibleBounds, layer, seatLayer);
  }

  /**
   * Gets the appropriate Level of Detail based on zoom scale
   */
  private getCurrentLODLevel(scale: number): LODLevel {
    // Find the appropriate LOD level based on scale
    for (let i = this.virtualizationConfig.lodLevels.length - 1; i >= 0; i--) {
      if (scale >= this.virtualizationConfig.lodLevels[i].scale) {
        return this.virtualizationConfig.lodLevels[i];
      }
    }
    return this.virtualizationConfig.lodLevels[0];
  }

  /**
   * Calculates the visible bounds of the viewport
   */
  private getVisibleBounds(viewport: ViewportState, stage: Konva.Stage): {
    x: number;
    y: number;
    width: number;
    height: number;
  } {
    const stageWidth = stage.width();
    const stageHeight = stage.height();
    const scale = viewport.scale;
    const position = viewport.position;

    return {
      x: -position.x / scale,
      y: -position.y / scale,
      width: stageWidth / scale,
      height: stageHeight / scale
    };
  }

  /**
   * Renders venue with virtualization and LOD
   */
  private renderWithVirtualization(
    venue: Venue,
    lod: LODLevel,
    visibleBounds: any,
    layer: Konva.Layer,
    seatLayer: Konva.Layer
  ): void {
    // Clear existing content
    layer.destroyChildren();
    seatLayer.destroyChildren();

    // Render sections
    venue.sections.forEach(section => {
      if (this.isInViewport(section, visibleBounds)) {
        this.renderSectionWithLOD(section, lod, visibleBounds, layer, seatLayer);
      }
    });

    // Batch draw for performance
    this.queueRender(() => {
      layer.batchDraw();
      seatLayer.batchDraw();
    });
  }

  /**
   * Renders a section with appropriate LOD
   */
  private renderSectionWithLOD(
    section: Section,
    lod: LODLevel,
    visibleBounds: any,
    layer: Konva.Layer,
    seatLayer: Konva.Layer
  ): void {
    // Always render section outline
    const sectionRect = new Konva.Rect({
      x: section.position.x,
      y: section.position.y,
      width: section.size.width,
      height: section.size.height,
      fill: section.color,
      stroke: '#333',
      strokeWidth: lod.simplifyShapes ? 1 : 2,
      opacity: 0.3,
      id: `section-${section.id}`
    });

    layer.add(sectionRect);

    // Add section label
    const label = new Konva.Text({
      x: section.position.x + 10,
      y: section.position.y + 10,
      text: section.name,
      fontSize: lod.simplifyShapes ? 12 : 16,
      fontFamily: 'Arial',
      fill: '#333'
    });

    layer.add(label);

    // Render rows based on LOD
    if (lod.renderSeats) {
      this.renderRowsWithLOD(section.rows, lod, visibleBounds, seatLayer);
    } else {
      // Just show capacity info for very zoomed out views
      const capacityLabel = new Konva.Text({
        x: section.position.x + section.size.width / 2 - 30,
        y: section.position.y + section.size.height / 2,
        text: `${section.capacity} seats`,
        fontSize: 14,
        fontFamily: 'Arial',
        fill: '#666',
        align: 'center'
      });

      layer.add(capacityLabel);
    }
  }

  /**
   * Renders rows with LOD considerations
   */
  private renderRowsWithLOD(
    rows: Row[],
    lod: LODLevel,
    visibleBounds: any,
    seatLayer: Konva.Layer
  ): void {
    rows.forEach(row => {
      // Check if row is in viewport
      if (this.isRowInViewport(row, visibleBounds)) {
        // Render row label if needed
        if (lod.renderRowLabels && row.seats.length > 0) {
          const firstSeat = row.seats[0];
          const rowLabel = new Konva.Text({
            x: firstSeat.position.x - 30,
            y: firstSeat.position.y - 5,
            text: row.name,
            fontSize: 12,
            fontFamily: 'Arial',
            fill: '#666'
          });
          seatLayer.add(rowLabel);
        }

        // Render seats in chunks for performance
        this.renderSeatsInChunks(row.seats, lod, seatLayer);
      }
    });
  }

  /**
   * Renders seats in chunks to avoid blocking the UI
   */
  private renderSeatsInChunks(
    seats: Seat[],
    lod: LODLevel,
    seatLayer: Konva.Layer
  ): void {
    const chunkSize = this.virtualizationConfig.chunkSize;
    
    for (let i = 0; i < seats.length; i += chunkSize) {
      const chunk = seats.slice(i, i + chunkSize);
      
      this.queueRender(() => {
        chunk.forEach(seat => {
          this.renderSeatWithLOD(seat, lod, seatLayer);
        });
      });
    }
  }

  /**
   * Renders a single seat with LOD
   */
  private renderSeatWithLOD(seat: Seat, lod: LODLevel, seatLayer: Konva.Layer): void {
    const seatSize = lod.simplifyShapes ? 12 : 16;
    let fillColor = this.getSeatColor(seat.status);

    const circle = new Konva.Circle({
      x: seat.position.x,
      y: seat.position.y,
      radius: seatSize / 2,
      fill: fillColor,
      stroke: lod.simplifyShapes ? 'transparent' : '#333',
      strokeWidth: lod.simplifyShapes ? 0 : 1,
      id: `seat-${seat.id}`,
      name: 'seat'
    });

    seatLayer.add(circle);

    // Add seat number if LOD allows
    if (lod.renderSeatNumbers) {
      const text = new Konva.Text({
        x: seat.position.x - 6,
        y: seat.position.y - 6,
        text: seat.number,
        fontSize: 10,
        fontFamily: 'Arial',
        fill: '#fff'
      });
      seatLayer.add(text);
    }

    // Add interaction only for detailed views
    if (!lod.simplifyShapes) {
      circle.on('mouseenter', () => {
        circle.stroke('#000');
        circle.strokeWidth(2);
        seatLayer.draw();
      });

      circle.on('mouseleave', () => {
        circle.stroke('#333');
        circle.strokeWidth(1);
        seatLayer.draw();
      });
    }
  }

  /**
   * Fallback method to render all seats (for smaller venues)
   */
  private renderAllSeats(venue: Venue, layer: Konva.Layer, seatLayer: Konva.Layer): void {
    venue.sections.forEach(section => {
      this.renderSectionWithLOD(
        section,
        this.virtualizationConfig.lodLevels[this.virtualizationConfig.lodLevels.length - 1],
        null,
        layer,
        seatLayer
      );
    });

    layer.draw();
    seatLayer.draw();
  }

  /**
   * Checks if a section is in the viewport
   */
  private isInViewport(section: Section, bounds: any): boolean {
    if (!bounds) return true;

    return !(
      section.position.x > bounds.x + bounds.width ||
      section.position.x + section.size.width < bounds.x ||
      section.position.y > bounds.y + bounds.height ||
      section.position.y + section.size.height < bounds.y
    );
  }

  /**
   * Checks if a row is in the viewport
   */
  private isRowInViewport(row: Row, bounds: any): boolean {
    if (!bounds || row.seats.length === 0) return true;

    const firstSeat = row.seats[0];
    const lastSeat = row.seats[row.seats.length - 1];
    const rowBounds = {
      x: Math.min(firstSeat.position.x, lastSeat.position.x) - 50,
      y: firstSeat.position.y - 20,
      width: Math.abs(lastSeat.position.x - firstSeat.position.x) + 100,
      height: 40
    };

    return !(
      rowBounds.x > bounds.x + bounds.width ||
      rowBounds.x + rowBounds.width < bounds.x ||
      rowBounds.y > bounds.y + bounds.height ||
      rowBounds.y + rowBounds.height < bounds.y
    );
  }

  /**
   * Gets seat color based on status
   */
  private getSeatColor(status: string): string {
    switch (status) {
      case 'available': return '#4CAF50';
      case 'selected': return '#2196F3';
      case 'occupied': return '#F44336';
      case 'blocked': return '#9E9E9E';
      case 'wheelchair': return '#FF9800';
      default: return '#4CAF50';
    }
  }

  /**
   * Queues render operations to avoid blocking the UI
   */
  private queueRender(renderFn: () => void): void {
    this.renderQueue.push(renderFn);
    
    if (!this.isProcessingQueue) {
      this.processRenderQueue();
    }
  }

  /**
   * Processes the render queue using requestAnimationFrame
   */
  private processRenderQueue(): void {
    if (this.renderQueue.length === 0) {
      this.isProcessingQueue = false;
      return;
    }

    this.isProcessingQueue = true;
    
    this.frameId = requestAnimationFrame(() => {
      const startTime = performance.now();
      const maxTime = 16; // 16ms budget per frame (60fps)

      while (this.renderQueue.length > 0 && (performance.now() - startTime) < maxTime) {
        const renderFn = this.renderQueue.shift();
        if (renderFn) {
          renderFn();
        }
      }

      this.processRenderQueue();
    });
  }

  /**
   * Cancels any pending render operations
   */
  cancelPendingRenders(): void {
    this.renderQueue.length = 0;
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }
    this.isProcessingQueue = false;
  }

  /**
   * Updates virtualization configuration
   */
  updateVirtualizationConfig(config: Partial<VirtualizationConfig>): void {
    this.virtualizationConfig = { ...this.virtualizationConfig, ...config };
  }

  /**
   * Gets current virtualization configuration
   */
  getVirtualizationConfig(): VirtualizationConfig {
    return { ...this.virtualizationConfig };
  }
}
