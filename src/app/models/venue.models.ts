export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Seat {
  id: string;
  row: string;
  number: string;
  position: Point;
  status: SeatStatus;
  category: SeatCategory;
  price?: number;
  isAccessible?: boolean;
}

export interface Row {
  id: string;
  name: string;
  seats: Seat[];
  position: Point;
  angle: number;
  curve: number;
  sectionId: string;
}

export interface Section {
  id: string;
  name: string;
  type: SectionType;
  position: Point;
  size: Size;
  rows: Row[];
  color: string;
  capacity: number;
  isVisible: boolean;
  rotation: number;
}

export interface Venue {
  id: string;
  name: string;
  description?: string;
  sections: Section[];
  totalCapacity: number;
  canvasSize: Size;
  backgroundImage?: string;
  metadata: VenueMetadata;
}

export interface VenueMetadata {
  created: Date;
  modified: Date;
  version: string;
  author: string;
}

export enum SeatStatus {
  AVAILABLE = 'available',
  SELECTED = 'selected',
  OCCUPIED = 'occupied',
  BLOCKED = 'blocked',
  WHEELCHAIR = 'wheelchair'
}

export enum SeatCategory {
  STANDARD = 'standard',
  PREMIUM = 'premium',
  VIP = 'vip',
  WHEELCHAIR = 'wheelchair',
  COMPANION = 'companion'
}

export enum SectionType {
  ORCHESTRA = 'orchestra',
  BALCONY = 'balcony',
  MEZZANINE = 'mezzanine',
  BOX = 'box',
  STANDING = 'standing',
  CUSTOM = 'custom'
}

export interface ToolConfig {
  selectedTool: DesignTool;
  sectionConfig: SectionConfig;
  rowConfig: RowConfig;
  seatConfig: SeatConfig;
}

export interface SectionConfig {
  type: SectionType;
  name: string;
  color: string;
  width: number;
  height: number;
}

export interface RowConfig {
  seatsPerRow: number;
  rowSpacing: number;
  seatSpacing: number;
  startNumber: number;
  rowNaming: RowNaming;
  curve: number;
}

export interface SeatConfig {
  category: SeatCategory;
  price: number;
  size: number;
  isAccessible: boolean;
}

export enum DesignTool {
  SELECT = 'select',
  SECTION = 'section',
  ROW = 'row',
  SEAT = 'seat',
  DELETE = 'delete',
  PAN = 'pan',
  ZOOM = 'zoom'
}

export enum RowNaming {
  ALPHABETIC = 'alphabetic', // A, B, C...
  NUMERIC = 'numeric',       // 1, 2, 3...
  ROMAN = 'roman',          // I, II, III...
  CUSTOM = 'custom'         // Custom pattern
}

export interface ViewportState {
  scale: number;
  position: Point;
  minScale: number;
  maxScale: number;
}

export interface SelectionState {
  selectedSeats: string[];
  selectedSections: string[];
  selectedRows: string[];
  hoveredElement?: string;
  multiSelect: boolean;
}

export interface ContextMenuState {
  visible: boolean;
  x: number;
  y: number;
  targetType: 'seat' | 'row' | 'section' | 'canvas' | null;
  targetId: string | null;
  actions: ContextMenuAction[];
}

export interface ContextMenuAction {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
}

export interface VenueStats {
  totalSeats: number;
  availableSeats: number;
  selectedSeats: number;
  occupiedSeats: number;
  sectionStats: SectionStats[];
}

export interface SectionStats {
  sectionId: string;
  sectionName: string;
  totalSeats: number;
  availableSeats: number;
  selectedSeats: number;
  occupiedSeats: number;
}

export interface ExportOptions {
  format: ExportFormat;
  includeMetadata: boolean;
  includeImages: boolean;
  compression: boolean;
}

export enum ExportFormat {
  JSON = 'json',
  SVG = 'svg',
  PNG = 'png',
  PDF = 'pdf'
}

export interface ImportResult {
  success: boolean;
  venue?: Venue;
  errors: string[];
  warnings: string[];
}

// Performance optimization interfaces
export interface VirtualizationConfig {
  enabled: boolean;
  chunkSize: number;
  renderDistance: number;
  lodLevels: LODLevel[];
}

export interface LODLevel {
  scale: number;
  renderSeats: boolean;
  renderSeatNumbers: boolean;
  renderRowLabels: boolean;
  simplifyShapes: boolean;
}

// Event interfaces
export interface VenueEvent {
  type: VenueEventType;
  data: any;
  timestamp: Date;
}

export enum VenueEventType {
  SEAT_SELECTED = 'seat_selected',
  SEAT_DESELECTED = 'seat_deselected',
  SECTION_CREATED = 'section_created',
  SECTION_MODIFIED = 'section_modified',
  SECTION_DELETED = 'section_deleted',
  ROW_CREATED = 'row_created',
  ROW_MODIFIED = 'row_modified',
  ROW_DELETED = 'row_deleted',
  VIEWPORT_CHANGED = 'viewport_changed',
  TOOL_CHANGED = 'tool_changed'
}
