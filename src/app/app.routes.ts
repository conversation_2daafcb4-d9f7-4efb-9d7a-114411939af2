import { Routes } from '@angular/router';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';
import {DashboardComponent} from './pages/dashboard/dashboard/dashboard.component';
import {TestComponent} from './pages/test/test.component';
import {AddBuildingComponent} from './pages/buildings/add-building/add-building.component';


export const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
      },
      {
        path: 'test',
        component: TestComponent,
      },
      {
        path: 'buildings/add',
        component: AddBuildingComponent,
      },
      // Add more child routes here
    ],
  },
  // Optionally, add routes for login or error pages outside the main layout
];
