<div class="container-fluid py-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow">
        <div class="card-header bg-primary text-white">
          <h3 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>
            Προσθήκη Νέου Κτιρίου
          </h3>
        </div>
        
        <div class="card-body">
          <form [formGroup]="buildingForm" (ngSubmit)="onSubmit()">
            
            <!-- General Information Section -->
            <div class="section-header mb-4">
              <h4 class="text-primary border-bottom pb-2">
                <i class="fas fa-info-circle me-2"></i>
                Γενικές πληροφορίες
              </h4>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-12">
                <label for="address" class="form-label">Διεύθυνση</label>
                <input
                  type="text"
                  id="address"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid('address')"
                  formControlName="address"
                  placeholder="πχ. Φιλολάου 117, Παγκράτι"
                >
                <div class="invalid-feedback">
                  {{ getFieldError('address') }}
                </div>
              </div>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="apartmentCount" class="form-label">Αριθμός διαμερισμάτων</label>
                <input
                  type="number"
                  id="apartmentCount"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid('apartmentCount')"
                  formControlName="apartmentCount"
                  min="1"
                >
                <div class="invalid-feedback">
                  {{ getFieldError('apartmentCount') }}
                </div>
              </div>
              
              <div class="col-md-6">
                <label for="initialCashReserve" class="form-label">Αρχικό αποθεματικό μετρητών</label>
                <div class="input-group">
                  <input
                    type="number"
                    id="initialCashReserve"
                    class="form-control"
                    [class.is-invalid]="isFieldInvalid('initialCashReserve')"
                    formControlName="initialCashReserve"
                    min="0"
                    step="0.01"
                  >
                  <span class="input-group-text">€</span>
                </div>
                <div class="invalid-feedback">
                  {{ getFieldError('initialCashReserve') }}
                </div>
              </div>
            </div>
            
            <!-- Heating Information Section -->
            <div class="section-header mb-4 mt-5">
              <h4 class="text-primary border-bottom pb-2">
                <i class="fas fa-thermometer-half me-2"></i>
                Πληροφορίες για τη θέρμανση
              </h4>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="heatingType" class="form-label">Τύπος θέρμανσης</label>
                <select
                  id="heatingType"
                  class="form-select"
                  [class.is-invalid]="isFieldInvalid('heatingType')"
                  formControlName="heatingType"
                >
                  <option *ngFor="let type of heatingTypes" [value]="type.value">
                    {{ type.label }}
                  </option>
                </select>
                <div class="invalid-feedback">
                  {{ getFieldError('heatingType') }}
                </div>
              </div>
              
              <div class="col-md-6">
                <label for="closedApartments" class="form-label">Κλειστά διαμερίσματα</label>
                <input
                  type="number"
                  id="closedApartments"
                  class="form-control"
                  [class.is-invalid]="isFieldInvalid('closedApartments')"
                  formControlName="closedApartments"
                  min="0"
                >
                <div class="invalid-feedback">
                  {{ getFieldError('closedApartments') }}
                </div>
              </div>
            </div>
            
            <!-- Participation Options -->
            <div class="row mb-3">
              <div class="col-md-4">
                <label class="form-label">Συμμετοχή σε δαπάνες θέρμανσης</label>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInHeatingExpenses"
                    id="heatingExpensesNo"
                    value="no"
                    formControlName="participateInHeatingExpenses"
                  >
                  <label class="form-check-label" for="heatingExpensesNo">Όχι</label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInHeatingExpenses"
                    id="heatingExpensesYes"
                    value="yes"
                    formControlName="participateInHeatingExpenses"
                  >
                  <label class="form-check-label" for="heatingExpensesYes">Ναι</label>
                </div>
              </div>
              
              <div class="col-md-4">
                <label class="form-label">Συμμετοχή σε δαπάνες καυστήρα</label>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInBurnerExpenses"
                    id="burnerExpensesNo"
                    value="no"
                    formControlName="participateInBurnerExpenses"
                  >
                  <label class="form-check-label" for="burnerExpensesNo">Όχι</label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInBurnerExpenses"
                    id="burnerExpensesYes"
                    value="yes"
                    formControlName="participateInBurnerExpenses"
                  >
                  <label class="form-check-label" for="burnerExpensesYes">Ναι</label>
                </div>
              </div>
              
              <div class="col-md-4">
                <label class="form-label">Συμμετοχή σε λοιπά κοινόχρηστα</label>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInCommonExpenses"
                    id="commonExpensesNo"
                    value="no"
                    formControlName="participateInCommonExpenses"
                  >
                  <label class="form-check-label" for="commonExpensesNo">Όχι</label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="participateInCommonExpenses"
                    id="commonExpensesYes"
                    value="yes"
                    formControlName="participateInCommonExpenses"
                  >
                  <label class="form-check-label" for="commonExpensesYes">Ναι</label>
                </div>
              </div>
            </div>
            
            <!-- Documents Section -->
            <div class="section-header mb-4 mt-5">
              <h4 class="text-primary border-bottom pb-2">
                <i class="fas fa-file-alt me-2"></i>
                Καταστατικό και άλλα έγγραφα
              </h4>
            </div>
            
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="constitutionFile" class="form-label">
                  Καταστατικό πολυκατοικίας (προαιρετικό)
                </label>
                <input
                  type="file"
                  id="constitutionFile"
                  class="form-control"
                  accept=".pdf,.jpg,.jpeg,.png,.gif"
                  (change)="onConstitutionFileSelected($event)"
                >
                <div class="form-text">
                  Υποστηρίζονται αρχεία εικόνων και pdf με μέγεθος εως 10MB
                </div>
                <div *ngIf="selectedConstitutionFile" class="mt-2">
                  <span class="badge bg-success">
                    <i class="fas fa-check me-1"></i>
                    {{ selectedConstitutionFile.name }}
                  </span>
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-danger ms-2"
                    (click)="resetConstitutionFile()"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              
              <div class="col-md-6">
                <label for="documentsFile" class="form-label">
                  Ψηφιακά Έγγραφα (προαιρετικό)
                </label>
                <input
                  type="file"
                  id="documentsFile"
                  class="form-control"
                  accept=".pdf,.jpg,.jpeg,.png,.gif"
                  (change)="onDocumentsFileSelected($event)"
                >
                <div class="form-text">
                  Υποστηρίζονται αρχεία εικόνων και pdf με μέγεθος εως 10MB
                </div>
                <div *ngIf="selectedDocumentsFile" class="mt-2">
                  <span class="badge bg-success">
                    <i class="fas fa-check me-1"></i>
                    {{ selectedDocumentsFile.name }}
                  </span>
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-danger ms-2"
                    (click)="resetDocumentsFile()"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Submit Button -->
            <div class="row mt-4">
              <div class="col-12 text-end">
                <button
                  type="button"
                  class="btn btn-secondary me-2"
                  routerLink="/dashboard"
                >
                  <i class="fas fa-times me-1"></i>
                  Ακύρωση
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  [disabled]="buildingForm.invalid"
                >
                  <i class="fas fa-save me-1"></i>
                  Αποθήκευση Κτιρίου
                </button>
              </div>
            </div>
            
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
