.section-header {
  margin-top: 2rem;
}

.section-header h4 {
  font-weight: 600;
  color: #0d6efd;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.form-check-label {
  font-weight: 500;
  color: #495057;
}

.btn-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.invalid-feedback {
  font-size: 0.875rem;
  font-weight: 500;
}

.form-text {
  font-size: 0.8rem;
  color: #6c757d;
  font-style: italic;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  font-weight: 500;
}

/* Custom styling for file inputs */
.form-control[type="file"] {
  padding: 0.375rem 0.75rem;
}

.form-control[type="file"]::-webkit-file-upload-button {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  margin-right: 0.75rem;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .section-header {
    margin-top: 1.5rem;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .text-end {
    text-align: center !important;
  }
}

/* Animation for form validation */
.is-invalid {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Custom radio button styling */
.form-check {
  margin-bottom: 0.5rem;
}

.form-check-input {
  margin-top: 0.25rem;
}

/* Section dividers */
.border-bottom {
  border-bottom: 2px solid #e9ecef !important;
}

/* Success badge styling */
.badge.bg-success {
  background: linear-gradient(135deg, #198754 0%, #146c43 100%) !important;
}

/* File removal button */
.btn-outline-danger {
  border-radius: 50%;
  width: 30px;
  height: 30px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
