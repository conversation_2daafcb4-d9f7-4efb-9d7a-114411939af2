import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { AddBuildingComponent } from './add-building.component';

describe('AddBuildingComponent', () => {
  let component: AddBuildingComponent;
  let fixture: ComponentFixture<AddBuildingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddBuildingComponent, ReactiveFormsModule, RouterTestingModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AddBuildingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.buildingForm.get('address')?.value).toBe('');
    expect(component.buildingForm.get('apartmentCount')?.value).toBe('');
    expect(component.buildingForm.get('initialCashReserve')?.value).toBe(0);
    expect(component.buildingForm.get('heatingType')?.value).toBe('');
    expect(component.buildingForm.get('participateInHeatingExpenses')?.value).toBe('no');
    expect(component.buildingForm.get('participateInBurnerExpenses')?.value).toBe('no');
    expect(component.buildingForm.get('participateInCommonExpenses')?.value).toBe('no');
  });

  it('should validate required fields', () => {
    const form = component.buildingForm;
    
    expect(form.get('address')?.hasError('required')).toBeTruthy();
    expect(form.get('apartmentCount')?.hasError('required')).toBeTruthy();
    expect(form.get('heatingType')?.hasError('required')).toBeTruthy();
    
    form.get('address')?.setValue('Test Address 123');
    form.get('apartmentCount')?.setValue(5);
    form.get('heatingType')?.setValue('central');
    
    expect(form.get('address')?.hasError('required')).toBeFalsy();
    expect(form.get('apartmentCount')?.hasError('required')).toBeFalsy();
    expect(form.get('heatingType')?.hasError('required')).toBeFalsy();
  });

  it('should validate minimum values', () => {
    const form = component.buildingForm;
    
    form.get('apartmentCount')?.setValue(0);
    form.get('initialCashReserve')?.setValue(-100);
    form.get('closedApartments')?.setValue(-1);
    
    expect(form.get('apartmentCount')?.hasError('min')).toBeTruthy();
    expect(form.get('initialCashReserve')?.hasError('min')).toBeTruthy();
    expect(form.get('closedApartments')?.hasError('min')).toBeTruthy();
  });

  it('should validate address minimum length', () => {
    const addressControl = component.buildingForm.get('address');
    
    addressControl?.setValue('123');
    expect(addressControl?.hasError('minlength')).toBeTruthy();
    
    addressControl?.setValue('Valid Address 123');
    expect(addressControl?.hasError('minlength')).toBeFalsy();
  });

  it('should handle file selection', () => {
    const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const event = { target: { files: [mockFile] } };
    
    component.onConstitutionFileSelected(event);
    expect(component.selectedConstitutionFile).toBe(mockFile);
    
    component.onDocumentsFileSelected(event);
    expect(component.selectedDocumentsFile).toBe(mockFile);
  });

  it('should reset file selections', () => {
    const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    component.selectedConstitutionFile = mockFile;
    component.selectedDocumentsFile = mockFile;
    
    component.resetConstitutionFile();
    expect(component.selectedConstitutionFile).toBeNull();
    
    component.resetDocumentsFile();
    expect(component.selectedDocumentsFile).toBeNull();
  });

  it('should validate file types and sizes', () => {
    const validFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const invalidTypeFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const oversizedFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { type: 'application/pdf' });
    
    expect(component['isValidFile'](validFile)).toBeTruthy();
    expect(component['isValidFile'](invalidTypeFile)).toBeFalsy();
    expect(component['isValidFile'](oversizedFile)).toBeFalsy();
  });

  it('should return appropriate error messages', () => {
    const form = component.buildingForm;
    
    // Test required field error
    form.get('address')?.markAsTouched();
    expect(component.getFieldError('address')).toBe('Αυτό το πεδίο είναι υποχρεωτικό');
    
    // Test minlength error
    form.get('address')?.setValue('123');
    form.get('address')?.markAsTouched();
    expect(component.getFieldError('address')).toContain('Ελάχιστος αριθμός χαρακτήρων');
    
    // Test min value error
    form.get('apartmentCount')?.setValue(0);
    form.get('apartmentCount')?.markAsTouched();
    expect(component.getFieldError('apartmentCount')).toContain('Ελάχιστη τιμή');
  });

  it('should check if field is invalid', () => {
    const form = component.buildingForm;
    
    expect(component.isFieldInvalid('address')).toBeFalsy(); // Not touched yet
    
    form.get('address')?.markAsTouched();
    expect(component.isFieldInvalid('address')).toBeTruthy(); // Invalid and touched
    
    form.get('address')?.setValue('Valid Address 123');
    expect(component.isFieldInvalid('address')).toBeFalsy(); // Valid now
  });
});
