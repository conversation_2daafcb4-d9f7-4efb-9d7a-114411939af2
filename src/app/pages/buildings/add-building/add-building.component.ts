import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-add-building',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './add-building.component.html',
  styleUrls: ['./add-building.component.css']
})
export class AddBuildingComponent {
  buildingForm: FormGroup;
  selectedConstitutionFile: File | null = null;
  selectedDocumentsFile: File | null = null;

  heatingTypes = [
    { value: '', label: 'Επίλεξε τύπο θέρμανσης' },
    { value: 'central', label: 'Κεντρική θέρμανση' },
    { value: 'autonomous', label: 'Αυτόνομη θέρμανση' },
    { value: 'individual', label: 'Ατομική θέρμανση' },
    { value: 'none', label: 'Χωρ<PERSON><PERSON> θέρμανση' }
  ];

  constructor(private fb: FormBuilder) {
    this.buildingForm = this.fb.group({
      // General Information
      address: ['', [Validators.required, Validators.minLength(5)]],
      apartmentCount: ['', [Validators.required, Validators.min(1)]],
      initialCashReserve: [0, [Validators.required, Validators.min(0)]],
      
      // Heating Information
      heatingType: ['', Validators.required],
      closedApartments: ['', [Validators.min(0)]],
      participateInHeatingExpenses: ['no', Validators.required],
      participateInBurnerExpenses: ['no', Validators.required],
      participateInCommonExpenses: ['no', Validators.required],
      
      // Documents
      constitutionFile: [null],
      documentsFile: [null]
    });
  }

  onConstitutionFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file && this.isValidFile(file)) {
      this.selectedConstitutionFile = file;
      this.buildingForm.patchValue({ constitutionFile: file });
    } else {
      this.resetConstitutionFile();
    }
  }

  onDocumentsFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file && this.isValidFile(file)) {
      this.selectedDocumentsFile = file;
      this.buildingForm.patchValue({ documentsFile: file });
    } else {
      this.resetDocumentsFile();
    }
  }

  private isValidFile(file: File): boolean {
    const validTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    return validTypes.includes(file.type) && file.size <= maxSize;
  }

  resetConstitutionFile(): void {
    this.selectedConstitutionFile = null;
    this.buildingForm.patchValue({ constitutionFile: null });
  }

  resetDocumentsFile(): void {
    this.selectedDocumentsFile = null;
    this.buildingForm.patchValue({ documentsFile: null });
  }

  onSubmit(): void {
    if (this.buildingForm.valid) {
      const formData = new FormData();
      
      // Add form fields
      Object.keys(this.buildingForm.value).forEach(key => {
        if (key !== 'constitutionFile' && key !== 'documentsFile') {
          formData.append(key, this.buildingForm.value[key]);
        }
      });
      
      // Add files
      if (this.selectedConstitutionFile) {
        formData.append('constitutionFile', this.selectedConstitutionFile);
      }
      if (this.selectedDocumentsFile) {
        formData.append('documentsFile', this.selectedDocumentsFile);
      }
      
      console.log('Form submitted:', this.buildingForm.value);
      console.log('Files:', {
        constitution: this.selectedConstitutionFile,
        documents: this.selectedDocumentsFile
      });
      
      // Here you would typically send the data to a service
      alert('Το κτίριο καταχωρήθηκε επιτυχώς!');
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.buildingForm.controls).forEach(key => {
      const control = this.buildingForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.buildingForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Αυτό το πεδίο είναι υποχρεωτικό';
      }
      if (field.errors['minlength']) {
        return `Ελάχιστος αριθμός χαρακτήρων: ${field.errors['minlength'].requiredLength}`;
      }
      if (field.errors['min']) {
        return `Ελάχιστη τιμή: ${field.errors['min'].min}`;
      }
    }
    return '';
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.buildingForm.get(fieldName);
    return !!(field?.invalid && field.touched);
  }
}
