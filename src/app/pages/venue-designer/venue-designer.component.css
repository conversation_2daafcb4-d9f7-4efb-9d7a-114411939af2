.venue-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;
}

/* Toolbar Styles */
.toolbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toolbar h4 {
  color: white;
  margin: 0;
  font-weight: 600;
}

.toolbar .btn-group .btn {
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.toolbar .btn-group .btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.toolbar .btn-group .btn.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toolbar .badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

/* Main Content Layout */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.right-sidebar {
  border-right: none;
  border-left: 1px solid #dee2e6;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar-content {
  padding: 1rem;
}

/* Property Panel Styles */
.property-panel {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.panel-title {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 0.75rem 1rem;
  margin: 0;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.property-panel form {
  padding: 1rem;
}

.property-panel .form-label {
  font-weight: 500;
  color: #495057;
  font-size: 0.875rem;
}

.property-panel .form-control,
.property-panel .form-select {
  font-size: 0.875rem;
  border-radius: 4px;
}

.property-panel .btn {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Canvas Area */
.canvas-area {
  flex: 1;
  position: relative;
  background: #ffffff;
  overflow: hidden;
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: default;
}

/* Canvas Overlay */
.canvas-overlay {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 100;
  pointer-events: none;
}

.overlay-info {
  display: flex;
  gap: 0.5rem;
}

.overlay-info .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  backdrop-filter: blur(10px);
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Section Statistics */
.section-stats {
  max-height: 200px;
  overflow-y: auto;
}

.section-stat {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.section-stat:hover {
  background-color: #f8f9fa;
}

.section-stat:last-child {
  border-bottom: none;
}

.section-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.section-numbers {
  font-size: 0.875rem;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

/* Form Enhancements */
.form-control-color {
  width: 100%;
  height: 38px;
  padding: 0.375rem;
}

.form-range {
  margin-bottom: 0.5rem;
}

/* Button Enhancements */
.btn-group .btn {
  position: relative;
  transition: all 0.3s ease;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-group .btn.active {
  transform: translateY(0);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sidebar { width: 250px; }
  .stats-grid { grid-template-columns: 1fr; gap: 0.5rem; }
}

@media (max-width: 768px) {
  .main-content { flex-direction: column; }
  .sidebar { width: 100%; height: auto; max-height: 300px; }
  .toolbar { flex-wrap: wrap; gap: 0.5rem; }
  .canvas-area { min-height: 400px; }
}

/* Konva Canvas */
.konvajs-content {
  background: radial-gradient(circle at center, #ffffff 0%, #f8f9fa 100%);
  border-radius: 4px;
}

/* Scrollbar */
.sidebar::-webkit-scrollbar { width: 6px; }
.sidebar::-webkit-scrollbar-track { background: #f1f1f1; }
.sidebar::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 3px; }
.sidebar::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }

/* Context Menu */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  min-width: 180px;
  padding: 4px 0;
  font-size: 0.875rem;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.context-menu-item:hover:not(.disabled):not(.separator) {
  background-color: #f8f9fa;
}

.context-menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.context-menu-item.separator {
  height: 1px;
  background-color: #e9ecef;
  margin: 4px 0;
  padding: 0;
  cursor: default;
}

.context-menu-item i {
  width: 16px;
  text-align: center;
  color: #6c757d;
}

/* Keyboard Shortcuts */
.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.8rem;
}

.shortcut-item kbd {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 0.2rem 0.4rem;
  font-size: 0.75rem;
  color: #495057;
}

.shortcut-item span {
  color: #6c757d;
}

/* Enhanced Selection Styles */
.btn-group .btn {
  flex: 1;
}

.btn-group .btn i {
  font-size: 0.875rem;
}

/* Selection Indicators */
.selected-seat {
  stroke: #FFD700 !important;
  stroke-width: 3 !important;
}

.selected-section {
  stroke: #FFD700 !important;
  stroke-width: 4 !important;
  stroke-dasharray: 5,5 !important;
}

.selected-row {
  fill: #FFD700 !important;
  font-weight: bold !important;
}

/* Seat Status Colors */
.seat-available { color: #4CAF50; }
.seat-selected { color: #2196F3; }
.seat-occupied { color: #F44336; }
.seat-blocked { color: #9E9E9E; }
.seat-wheelchair { color: #FF9800; }

/* Professional Enhancements */
.property-panel .btn-group { border-radius: 4px; overflow: hidden; }
.property-panel .btn-group .btn { border-radius: 0; }
.property-panel .btn-group .btn:first-child { border-radius: 4px 0 0 4px; }
.property-panel .btn-group .btn:last-child { border-radius: 0 4px 4px 0; }

/* Canvas */
.canvas-container { user-select: none; }
.canvas-container:focus { outline: none; }

/* Context Menu Animation */
.context-menu { animation: contextMenuFadeIn 0.15s ease-out; }
@keyframes contextMenuFadeIn {
  from { opacity: 0; transform: scale(0.95) translateY(-5px); }
  to { opacity: 1; transform: scale(1) translateY(0); }
}

/* Polygon Drawing */
.polygon-drawing-info {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(33, 150, 243, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Enhanced Section Shapes */
.section-polygon {
  cursor: pointer;
  transition: all 0.3s ease;
}

.section-polygon:hover {
  opacity: 0.5 !important;
  stroke-width: 3 !important;
}

/* Zoom-based visibility indicators */
.seats-hidden-indicator {
  font-size: 0.8rem;
  opacity: 0.7;
  font-style: italic;
}
