import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { VenueDesignerComponent } from './venue-designer.component';
import { VenueService } from '../../services/venue.service';
import { VenuePerformanceService } from '../../services/venue-performance.service';
import { DesignTool } from '../../models/venue.models';

describe('VenueDesignerComponent', () => {
  let component: VenueDesignerComponent;
  let fixture: ComponentFixture<VenueDesignerComponent>;
  let venueService: jasmine.SpyObj<VenueService>;
  let performanceService: jasmine.SpyObj<VenuePerformanceService>;

  beforeEach(async () => {
    const venueServiceSpy = jasmine.createSpyObj('VenueService', [
      'createVenue',
      'createSection',
      'createMultipleRows',
      'selectSeat',
      'clearSelection',
      'setSelectedTool',
      'updateViewport',
      'zoomIn',
      'zoomOut',
      'resetZoom',
      'getVenueStats'
    ]);

    // Mock return values
    venueServiceSpy.createVenue.and.returnValue({
      id: 'test-venue',
      name: 'Test Venue',
      sections: [],
      totalCapacity: 0,
      canvasSize: { width: 1200, height: 800 },
      metadata: {
        created: new Date(),
        modified: new Date(),
        version: '1.0.0',
        author: 'Test'
      }
    });

    venueServiceSpy.createSection.and.returnValue({
      id: 'test-section',
      name: 'Test Section',
      type: 'orchestra' as any,
      position: { x: 100, y: 100 },
      size: { width: 600, height: 400 },
      rows: [],
      color: '#4CAF50',
      capacity: 0,
      isVisible: true,
      rotation: 0
    });

    const performanceServiceSpy = jasmine.createSpyObj('VenuePerformanceService', [
      'optimizeVenueRendering'
    ]);

    // Mock the observables
    venueServiceSpy.venue$ = of(null);
    venueServiceSpy.toolConfig$ = of({
      selectedTool: DesignTool.SELECT,
      sectionConfig: {
        type: 'orchestra' as any,
        name: 'Test',
        color: '#000',
        width: 100,
        height: 100
      },
      rowConfig: {
        seatsPerRow: 10,
        rowSpacing: 30,
        seatSpacing: 25,
        startNumber: 1,
        rowNaming: 'alphabetic' as any,
        curve: 0
      },
      seatConfig: {
        category: 'standard' as any,
        price: 50,
        size: 20,
        isAccessible: false
      }
    });
    venueServiceSpy.viewport$ = of({
      scale: 1,
      position: { x: 0, y: 0 },
      minScale: 0.1,
      maxScale: 5
    });
    venueServiceSpy.selection$ = of({
      selectedSeats: [],
      selectedSections: [],
      selectedRows: [],
      multiSelect: false
    });
    venueServiceSpy.contextMenu$ = of({
      visible: false,
      x: 0,
      y: 0,
      targetType: null,
      targetId: null,
      actions: []
    });

    await TestBed.configureTestingModule({
      imports: [VenueDesignerComponent, ReactiveFormsModule],
      providers: [
        { provide: VenueService, useValue: venueServiceSpy },
        { provide: VenuePerformanceService, useValue: performanceServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(VenueDesignerComponent);
    component = fixture.componentInstance;
    venueService = TestBed.inject(VenueService) as jasmine.SpyObj<VenueService>;
    performanceService = TestBed.inject(VenuePerformanceService) as jasmine.SpyObj<VenuePerformanceService>;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize forms with default values', () => {
    expect(component.venueForm.get('name')?.value).toBe('New Venue');
    expect(component.sectionForm.get('name')?.value).toBe('New Section');
    expect(component.rowForm.get('seatsPerRow')?.value).toBe(20);
  });

  it('should call venueService.setSelectedTool when selectTool is called', () => {
    component.selectTool(DesignTool.SELECT);
    expect(venueService.setSelectedTool).toHaveBeenCalledWith(DesignTool.SELECT);
  });

  it('should call venueService.zoomIn when zoomIn is called', () => {
    component.zoomIn();
    expect(venueService.zoomIn).toHaveBeenCalled();
  });

  it('should call venueService.zoomOut when zoomOut is called', () => {
    component.zoomOut();
    expect(venueService.zoomOut).toHaveBeenCalled();
  });

  it('should call venueService.resetZoom when resetZoom is called', () => {
    component.resetZoom();
    expect(venueService.resetZoom).toHaveBeenCalled();
  });

  it('should call venueService.clearSelection when clearSelection is called', () => {
    component.clearSelection();
    expect(venueService.clearSelection).toHaveBeenCalled();
  });
});
