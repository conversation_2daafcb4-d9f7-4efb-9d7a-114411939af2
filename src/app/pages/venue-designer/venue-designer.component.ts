import { Compo<PERSON>, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import Konva from 'konva';

import { VenueService } from '../../services/venue.service';
import { VenuePerformanceService } from '../../services/venue-performance.service';
import {
  Venue, Section, Row, Seat, Point, Size,
  DesignTool, SectionType, RowNaming, ViewportState,
  ToolConfig, SelectionState, VenueStats, ContextMenuState,
  SectionShape, PolygonDrawingState
} from '../../models/venue.models';

@Component({
  selector: 'app-venue-designer',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './venue-designer.component.html',
  styleUrls: ['./venue-designer.component.css']
})
export class VenueDesignerComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('canvasContainer', { static: true }) canvasContainer!: ElementRef<HTMLDivElement>;

  private destroy$ = new Subject<void>();
  private stage!: Konva.Stage;
  private layer!: Konva.Layer;
  private backgroundLayer!: Konva.Layer;
  private seatLayer!: Konva.Layer;
  private uiLayer!: Konva.Layer;

  // Component state
  venue: Venue | null = null;
  toolConfig: ToolConfig | null = null;
  viewport: ViewportState | null = null;
  selection: SelectionState | null = null;
  contextMenu: ContextMenuState | null = null;
  stats: VenueStats | null = null;

  // Forms
  venueForm!: FormGroup;
  sectionForm!: FormGroup;
  rowForm!: FormGroup;

  // UI State
  showToolbar = true;
  showProperties = true;
  showStats = true;
  isLoading = false;

  // Enums for template
  DesignTool = DesignTool;
  SectionType = SectionType;
  RowNaming = RowNaming;

  constructor(
    private venueService: VenueService,
    private venuePerformanceService: VenuePerformanceService,
    private fb: FormBuilder
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.subscribeToServices();
    this.createDefaultVenue();
  }

  ngAfterViewInit(): void {
    this.initializeCanvas();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.stage) {
      this.stage.destroy();
    }
  }

  private initializeForms(): void {
    this.venueForm = this.fb.group({
      name: ['New Venue'],
      description: [''],
      width: [1200],
      height: [800]
    });

    this.sectionForm = this.fb.group({
      name: ['New Section'],
      type: [SectionType.ORCHESTRA],
      color: ['#4CAF50'],
      width: [300],
      height: [200]
    });

    this.rowForm = this.fb.group({
      seatsPerRow: [20],
      rowCount: [10],
      rowSpacing: [40],
      seatSpacing: [30],
      rowNaming: [RowNaming.ALPHABETIC],
      curve: [0]
    });
  }

  private subscribeToServices(): void {
    // Subscribe to venue changes
    this.venueService.venue$
      .pipe(takeUntil(this.destroy$))
      .subscribe(venue => {
        this.venue = venue;
        this.updateStats();
        this.renderVenue();
      });

    // Subscribe to tool config changes
    this.venueService.toolConfig$
      .pipe(takeUntil(this.destroy$))
      .subscribe(config => {
        this.toolConfig = config;
        this.updateCursor();
      });

    // Subscribe to viewport changes
    this.venueService.viewport$
      .pipe(takeUntil(this.destroy$))
      .subscribe(viewport => {
        this.viewport = viewport;
        this.updateViewport();
      });

    // Subscribe to selection changes
    this.venueService.selection$
      .pipe(takeUntil(this.destroy$))
      .subscribe(selection => {
        this.selection = selection;
        this.updateSelection();
      });

    // Subscribe to context menu changes
    this.venueService.contextMenu$
      .pipe(takeUntil(this.destroy$))
      .subscribe(contextMenu => {
        this.contextMenu = contextMenu;
      });
  }

  private initializeCanvas(): void {
    const container = this.canvasContainer.nativeElement;
    const containerRect = container.getBoundingClientRect();

    this.stage = new Konva.Stage({
      container: container,
      width: containerRect.width,
      height: containerRect.height,
      draggable: true
    });

    // Create layers
    this.backgroundLayer = new Konva.Layer();
    this.layer = new Konva.Layer();
    this.seatLayer = new Konva.Layer();
    this.uiLayer = new Konva.Layer();

    this.stage.add(this.backgroundLayer);
    this.stage.add(this.layer);
    this.stage.add(this.seatLayer);
    this.stage.add(this.uiLayer);

    this.setupEventHandlers();
    this.addGrid();
  }

  private setupEventHandlers(): void {
    // Mouse events
    this.stage.on('click', (e) => this.handleStageClick(e));
    this.stage.on('contextmenu', (e) => this.handleContextMenu(e));
    this.stage.on('mousemove', (e) => this.handleMouseMove(e));
    this.stage.on('wheel', (e) => this.handleWheel(e));

    // Window resize
    window.addEventListener('resize', () => this.handleResize());

    // Global click to hide context menu
    document.addEventListener('click', (e) => this.handleDocumentClick(e));

    // Keyboard events
    document.addEventListener('keydown', (e) => this.handleKeyDown(e));
  }

  private handleStageClick(e: Konva.KonvaEventObject<MouseEvent>): void {
    if (!this.toolConfig) return;

    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    switch (this.toolConfig.selectedTool) {
      case DesignTool.SECTION:
        this.createSectionAtPosition(pos);
        break;
      case DesignTool.SELECT:
        this.handleSelection(e);
        break;
    }
  }

  private handleMouseMove(e: Konva.KonvaEventObject<MouseEvent>): void {
    // Handle hover effects and cursor updates
    this.updateCursor();
  }

  private handleWheel(e: Konva.KonvaEventObject<WheelEvent>): void {
    e.evt.preventDefault();

    const scaleBy = 1.05;
    const stage = this.stage;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    if (!pointer) return;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    const newScale = e.evt.deltaY > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    // Update viewport through service
    this.venueService.updateViewport({
      scale: newScale,
      position: {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale
      }
    });
  }

  private handleResize(): void {
    const container = this.canvasContainer.nativeElement;
    const containerRect = container.getBoundingClientRect();
    
    this.stage.width(containerRect.width);
    this.stage.height(containerRect.height);
  }

  private createSectionAtPosition(pos: Point): void {
    if (!this.toolConfig) return;

    const config = this.toolConfig.sectionConfig;
    this.venueService.createSection(
      config.type,
      config.name,
      pos,
      { width: config.width, height: config.height },
      config.color
    );
  }

  private handleContextMenu(e: Konva.KonvaEventObject<MouseEvent>): void {
    e.evt.preventDefault();

    const pos = this.stage.getPointerPosition();
    if (!pos) return;

    const target = e.target;
    const objectId = target.id();

    if (objectId) {
      // Right-clicked on an object
      if (objectId.startsWith('seat-')) {
        const seatId = objectId.replace('seat-', '');
        this.venueService.showContextMenu(pos.x, pos.y, 'seat', seatId);
      } else if (objectId.startsWith('section-')) {
        const sectionId = objectId.replace('section-', '');
        this.venueService.showContextMenu(pos.x, pos.y, 'section', sectionId);
      } else if (objectId.startsWith('row-')) {
        const rowId = objectId.replace('row-', '');
        this.venueService.showContextMenu(pos.x, pos.y, 'row', rowId);
      }
    } else {
      // Right-clicked on empty canvas
      this.venueService.showContextMenu(pos.x, pos.y, 'canvas');
    }
  }

  private handleSelection(e: Konva.KonvaEventObject<MouseEvent>): void {
    const target = e.target;
    const isMultiSelect = e.evt.ctrlKey || e.evt.metaKey;

    if (target === this.stage) {
      // Clicked on empty space - clear selection unless multi-selecting
      if (!isMultiSelect) {
        this.venueService.clearSelection();
      }
    } else {
      // Handle object selection
      const objectId = target.id();
      if (objectId) {
        // Determine object type and handle accordingly
        if (objectId.startsWith('seat-')) {
          const seatId = objectId.replace('seat-', '');
          this.venueService.selectSeat(seatId, isMultiSelect);
        } else if (objectId.startsWith('section-')) {
          const sectionId = objectId.replace('section-', '');
          this.venueService.selectSection(sectionId, isMultiSelect);
        } else if (objectId.startsWith('row-')) {
          const rowId = objectId.replace('row-', '');
          this.venueService.selectRow(rowId, isMultiSelect);
        }
      }
    }
  }

  private handleDocumentClick(e: MouseEvent): void {
    // Hide context menu when clicking outside
    if (this.contextMenu?.visible) {
      const contextMenuElement = document.querySelector('.context-menu');
      if (contextMenuElement && !contextMenuElement.contains(e.target as Node)) {
        this.venueService.hideContextMenu();
      }
    }
  }

  private handleKeyDown(e: KeyboardEvent): void {
    // Handle keyboard shortcuts
    switch (e.key) {
      case 'Delete':
      case 'Backspace':
        this.deleteSelected();
        break;
      case 'Escape':
        this.venueService.clearSelection();
        this.venueService.hideContextMenu();
        break;
      case 'a':
      case 'A':
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          this.selectAll();
        }
        break;
    }
  }

  deleteSelected(): void {
    if (!this.selection) return;

    // Delete selected seats
    this.selection.selectedSeats.forEach(seatId => {
      this.venueService.deleteSeat(seatId);
    });

    // Delete selected rows
    this.selection.selectedRows.forEach(rowId => {
      this.venueService.deleteRow(rowId);
    });

    // Delete selected sections
    this.selection.selectedSections.forEach(sectionId => {
      this.venueService.deleteSection(sectionId);
    });
  }

  private selectAll(): void {
    if (!this.venue) return;

    // Select all sections
    this.venue.sections.forEach(section => {
      this.venueService.selectSection(section.id, true);
    });
  }

  private addGrid(): void {
    const gridSize = 20;
    const width = this.stage.width();
    const height = this.stage.height();

    // Vertical lines
    for (let i = 0; i < width / gridSize; i++) {
      const line = new Konva.Line({
        points: [i * gridSize, 0, i * gridSize, height],
        stroke: '#ddd',
        strokeWidth: 0.5,
        opacity: 0.3
      });
      this.backgroundLayer.add(line);
    }

    // Horizontal lines
    for (let i = 0; i < height / gridSize; i++) {
      const line = new Konva.Line({
        points: [0, i * gridSize, width, i * gridSize],
        stroke: '#ddd',
        strokeWidth: 0.5,
        opacity: 0.3
      });
      this.backgroundLayer.add(line);
    }

    this.backgroundLayer.draw();
  }

  private renderVenue(): void {
    if (!this.venue || !this.viewport) return;

    // Use performance service for optimized rendering
    this.venuePerformanceService.optimizeVenueRendering(
      this.venue,
      this.viewport,
      this.stage,
      this.layer,
      this.seatLayer
    );
  }

  private renderSection(section: Section): void {
    // Create section rectangle
    const rect = new Konva.Rect({
      x: section.position.x,
      y: section.position.y,
      width: section.size.width,
      height: section.size.height,
      fill: section.color,
      stroke: '#333',
      strokeWidth: 2,
      opacity: 0.3,
      id: `section-${section.id}`,
      name: 'section',
      draggable: true
    });

    // Add section label
    const label = new Konva.Text({
      x: section.position.x + 10,
      y: section.position.y + 10,
      text: section.name,
      fontSize: 16,
      fontFamily: 'Arial',
      fill: '#333'
    });

    this.layer.add(rect);
    this.layer.add(label);

    // Render rows and seats
    section.rows.forEach(row => {
      this.renderRow(row, section);
    });
  }

  private renderRow(row: Row, section: Section): void {
    row.seats.forEach(seat => {
      this.renderSeat(seat, section);
    });

    // Add row label
    if (row.seats.length > 0) {
      const firstSeat = row.seats[0];
      const label = new Konva.Text({
        x: firstSeat.position.x - 30,
        y: firstSeat.position.y - 5,
        text: row.name,
        fontSize: 12,
        fontFamily: 'Arial',
        fill: '#666',
        id: `row-label-${row.id}`,
        name: 'row-label'
      });
      this.seatLayer.add(label);
    }
  }

  private renderSeat(seat: Seat, section: Section): void {
    const seatSize = 16;
    let fillColor = '#4CAF50'; // Available
    
    switch (seat.status) {
      case 'selected':
        fillColor = '#2196F3';
        break;
      case 'occupied':
        fillColor = '#F44336';
        break;
      case 'blocked':
        fillColor = '#9E9E9E';
        break;
      case 'wheelchair':
        fillColor = '#FF9800';
        break;
    }

    const circle = new Konva.Circle({
      x: seat.position.x,
      y: seat.position.y,
      radius: seatSize / 2,
      fill: fillColor,
      stroke: '#333',
      strokeWidth: 1,
      id: `seat-${seat.id}`,
      name: 'seat'
    });

    // Add seat number
    const text = new Konva.Text({
      x: seat.position.x - 6,
      y: seat.position.y - 6,
      text: seat.number,
      fontSize: 10,
      fontFamily: 'Arial',
      fill: '#fff'
    });

    // Add hover effects
    circle.on('mouseenter', () => {
      circle.stroke('#000');
      circle.strokeWidth(2);
      this.seatLayer.draw();
    });

    circle.on('mouseleave', () => {
      circle.stroke('#333');
      circle.strokeWidth(1);
      this.seatLayer.draw();
    });

    this.seatLayer.add(circle);
    this.seatLayer.add(text);
  }

  private updateViewport(): void {
    if (!this.viewport) return;

    this.stage.scale({ x: this.viewport.scale, y: this.viewport.scale });
    this.stage.position(this.viewport.position);
  }

  private updateSelection(): void {
    if (!this.selection) return;

    // Update seat selection indicators
    this.seatLayer.find('.seat').forEach((node: any) => {
      const seatId = node.id().replace('seat-', '');
      if (this.selection?.selectedSeats.includes(seatId)) {
        node.stroke('#FFD700');
        node.strokeWidth(3);
      } else {
        node.stroke('#333');
        node.strokeWidth(1);
      }
    });

    // Update section selection indicators
    this.layer.find('.section').forEach((node: any) => {
      const sectionId = node.id().replace('section-', '');
      if (this.selection?.selectedSections.includes(sectionId)) {
        node.stroke('#FFD700');
        node.strokeWidth(4);
        node.dash([5, 5]);
      } else {
        node.stroke('#333');
        node.strokeWidth(2);
        node.dash([]);
      }
    });

    // Update row selection indicators
    this.seatLayer.find('.row-label').forEach((node: any) => {
      const rowId = node.id().replace('row-label-', '');
      if (this.selection?.selectedRows.includes(rowId)) {
        node.fill('#FFD700');
        node.fontStyle('bold');
      } else {
        node.fill('#666');
        node.fontStyle('normal');
      }
    });

    this.layer.draw();
    this.seatLayer.draw();
  }

  private updateCursor(): void {
    if (!this.toolConfig) return;

    let cursor = 'default';
    switch (this.toolConfig.selectedTool) {
      case DesignTool.SELECT:
        cursor = 'pointer';
        break;
      case DesignTool.SECTION:
        cursor = 'crosshair';
        break;
      case DesignTool.DELETE:
        cursor = 'not-allowed';
        break;
      case DesignTool.PAN:
        cursor = 'move';
        break;
    }

    this.stage.container().style.cursor = cursor;
  }

  private createDefaultVenue(): void {
    const venue = this.venueService.createVenue('New Venue', { width: 1200, height: 800 });
    
    // Create a sample section with rows
    const section = this.venueService.createSection(
      SectionType.ORCHESTRA,
      'Orchestra',
      { x: 100, y: 100 },
      { width: 600, height: 400 },
      '#4CAF50'
    );

    // Add some sample rows
    this.venueService.createMultipleRows(
      section.id,
      { x: 150, y: 150 },
      10, // 10 rows
      25, // 25 seats per row
      35, // row spacing
      25, // seat spacing
      RowNaming.ALPHABETIC
    );
  }

  private updateStats(): void {
    this.stats = this.venueService.getVenueStats();
  }

  // Public methods for template
  selectTool(tool: DesignTool): void {
    this.venueService.setSelectedTool(tool);
  }

  createSection(): void {
    if (!this.sectionForm.valid) return;

    const formValue = this.sectionForm.value;
    this.venueService.createSection(
      formValue.type,
      formValue.name,
      { x: 100, y: 100 }, // Default position
      { width: formValue.width, height: formValue.height },
      formValue.color
    );
  }

  createRows(): void {
    if (!this.rowForm.valid || !this.venue?.sections.length) return;

    const formValue = this.rowForm.value;
    const firstSection = this.venue.sections[0]; // Use first section for demo

    this.venueService.createMultipleRows(
      firstSection.id,
      { x: firstSection.position.x + 50, y: firstSection.position.y + 50 },
      formValue.rowCount,
      formValue.seatsPerRow,
      formValue.rowSpacing,
      formValue.seatSpacing,
      formValue.rowNaming,
      formValue.curve
    );
  }

  zoomIn(): void {
    this.venueService.zoomIn();
  }

  zoomOut(): void {
    this.venueService.zoomOut();
  }

  resetZoom(): void {
    this.venueService.resetZoom();
  }

  clearSelection(): void {
    this.venueService.clearSelection();
  }

  exportVenue(): void {
    this.venueService.exportVenueToJson();
  }

  exportSelectedToJson(): void {
    if (!this.selection || !this.venue) return;

    const selectedData = {
      selectedSeats: this.selection.selectedSeats,
      selectedSections: this.selection.selectedSections,
      selectedRows: this.selection.selectedRows,
      seatDetails: this.getSelectedSeatsDetails(),
      sectionDetails: this.getSelectedSectionsDetails(),
      rowDetails: this.getSelectedRowsDetails(),
      exportedAt: new Date().toISOString()
    };

    console.log('🎯 Selected Elements Export:', JSON.stringify(selectedData, null, 2));
  }

  private getSelectedSeatsDetails(): any[] {
    if (!this.venue || !this.selection) return [];

    const seatDetails: any[] = [];
    for (const section of this.venue.sections) {
      for (const row of section.rows) {
        for (const seat of row.seats) {
          if (this.selection.selectedSeats.includes(seat.id)) {
            seatDetails.push({
              id: seat.id,
              row: seat.row,
              number: seat.number,
              position: seat.position,
              status: seat.status,
              category: seat.category,
              sectionName: section.name,
              sectionId: section.id
            });
          }
        }
      }
    }
    return seatDetails;
  }

  private getSelectedSectionsDetails(): any[] {
    if (!this.venue || !this.selection) return [];

    return this.venue.sections
      .filter(section => this.selection!.selectedSections.includes(section.id))
      .map(section => ({
        id: section.id,
        name: section.name,
        type: section.type,
        position: section.position,
        size: section.size,
        capacity: section.capacity,
        rowCount: section.rows.length
      }));
  }

  private getSelectedRowsDetails(): any[] {
    if (!this.venue || !this.selection) return [];

    const rowDetails: any[] = [];
    for (const section of this.venue.sections) {
      for (const row of section.rows) {
        if (this.selection.selectedRows.includes(row.id)) {
          rowDetails.push({
            id: row.id,
            name: row.name,
            position: row.position,
            seatCount: row.seats.length,
            sectionName: section.name,
            sectionId: section.id
          });
        }
      }
    }
    return rowDetails;
  }

  importVenue(event: any): void {
    // Implementation for import functionality
    const file = event.target.files[0];
    if (file) {
      console.log('Import venue from file:', file);
    }
  }
}
