<div class="venue-designer">
  <!-- Top Toolbar -->
  <div class="toolbar" *ngIf="showToolbar">
    <div class="toolbar-section">
      <h4 class="mb-0">
        <i class="fas fa-theater-masks me-2"></i>
        Venue Designer
      </h4>
    </div>
    
    <div class="toolbar-section">
      <div class="btn-group" role="group">
        <button 
          type="button" 
          class="btn btn-outline-primary"
          [class.active]="toolConfig?.selectedTool === DesignTool.SELECT"
          (click)="selectTool(DesignTool.SELECT)"
          title="Select Tool">
          <i class="fas fa-mouse-pointer"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary"
          [class.active]="toolConfig?.selectedTool === DesignTool.SECTION"
          (click)="selectTool(DesignTool.SECTION)"
          title="Add Section">
          <i class="fas fa-square"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary"
          [class.active]="toolConfig?.selectedTool === DesignTool.ROW"
          (click)="selectTool(DesignTool.ROW)"
          title="Add Row">
          <i class="fas fa-grip-lines"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary"
          [class.active]="toolConfig?.selectedTool === DesignTool.SEAT"
          (click)="selectTool(DesignTool.SEAT)"
          title="Add Seat">
          <i class="fas fa-chair"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-danger"
          [class.active]="toolConfig?.selectedTool === DesignTool.DELETE"
          (click)="selectTool(DesignTool.DELETE)"
          title="Delete Tool">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>

    <div class="toolbar-section">
      <div class="btn-group" role="group">
        <button 
          type="button" 
          class="btn btn-outline-secondary"
          (click)="zoomIn()"
          title="Zoom In">
          <i class="fas fa-search-plus"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-secondary"
          (click)="zoomOut()"
          title="Zoom Out">
          <i class="fas fa-search-minus"></i>
        </button>
        <button 
          type="button" 
          class="btn btn-outline-secondary"
          (click)="resetZoom()"
          title="Reset Zoom">
          <i class="fas fa-expand-arrows-alt"></i>
        </button>
      </div>
    </div>

    <div class="toolbar-section">
      <div class="btn-group" role="group">
        <button 
          type="button" 
          class="btn btn-outline-success"
          (click)="exportVenue()"
          title="Export Venue">
          <i class="fas fa-download"></i>
        </button>
        <label class="btn btn-outline-info" title="Import Venue">
          <i class="fas fa-upload"></i>
          <input type="file" class="d-none" (change)="importVenue($event)" accept=".json">
        </label>
      </div>
    </div>

    <div class="toolbar-section ms-auto">
      <span class="badge bg-primary me-2" *ngIf="stats">
        Total Seats: {{ stats.totalSeats | number }}
      </span>
      <span class="badge bg-success me-2" *ngIf="stats">
        Available: {{ stats.availableSeats | number }}
      </span>
      <span class="badge bg-info" *ngIf="stats && stats.selectedSeats > 0">
        Selected: {{ stats.selectedSeats | number }}
      </span>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Left Sidebar - Properties Panel -->
    <div class="sidebar left-sidebar" *ngIf="showProperties">
      <div class="sidebar-content">
        
        <!-- Venue Properties -->
        <div class="property-panel">
          <h6 class="panel-title">
            <i class="fas fa-building me-2"></i>
            Venue Properties
          </h6>
          <form [formGroup]="venueForm" class="mt-3">
            <div class="mb-3">
              <label class="form-label">Venue Name</label>
              <input type="text" class="form-control form-control-sm" formControlName="name">
            </div>
            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea class="form-control form-control-sm" formControlName="description" rows="2"></textarea>
            </div>
            <div class="row">
              <div class="col-6">
                <label class="form-label">Width</label>
                <input type="number" class="form-control form-control-sm" formControlName="width">
              </div>
              <div class="col-6">
                <label class="form-label">Height</label>
                <input type="number" class="form-control form-control-sm" formControlName="height">
              </div>
            </div>
          </form>
        </div>

        <!-- Section Properties -->
        <div class="property-panel">
          <h6 class="panel-title">
            <i class="fas fa-square me-2"></i>
            Add Section
          </h6>
          <form [formGroup]="sectionForm" class="mt-3">
            <div class="mb-3">
              <label class="form-label">Section Name</label>
              <input type="text" class="form-control form-control-sm" formControlName="name">
            </div>
            <div class="mb-3">
              <label class="form-label">Section Type</label>
              <select class="form-select form-select-sm" formControlName="type">
                <option [value]="SectionType.ORCHESTRA">Orchestra</option>
                <option [value]="SectionType.BALCONY">Balcony</option>
                <option [value]="SectionType.MEZZANINE">Mezzanine</option>
                <option [value]="SectionType.BOX">Box</option>
                <option [value]="SectionType.STANDING">Standing</option>
                <option [value]="SectionType.CUSTOM">Custom</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Color</label>
              <input type="color" class="form-control form-control-color form-control-sm" formControlName="color">
            </div>
            <div class="row mb-3">
              <div class="col-6">
                <label class="form-label">Width</label>
                <input type="number" class="form-control form-control-sm" formControlName="width">
              </div>
              <div class="col-6">
                <label class="form-label">Height</label>
                <input type="number" class="form-control form-control-sm" formControlName="height">
              </div>
            </div>
            <button type="button" class="btn btn-primary btn-sm w-100" (click)="createSection()">
              <i class="fas fa-plus me-1"></i>
              Add Section
            </button>
          </form>
        </div>

        <!-- Row Properties -->
        <div class="property-panel">
          <h6 class="panel-title">
            <i class="fas fa-grip-lines me-2"></i>
            Add Rows
          </h6>
          <form [formGroup]="rowForm" class="mt-3">
            <div class="row mb-3">
              <div class="col-6">
                <label class="form-label">Seats per Row</label>
                <input type="number" class="form-control form-control-sm" formControlName="seatsPerRow" min="1" max="100">
              </div>
              <div class="col-6">
                <label class="form-label">Row Count</label>
                <input type="number" class="form-control form-control-sm" formControlName="rowCount" min="1" max="50">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-6">
                <label class="form-label">Row Spacing</label>
                <input type="number" class="form-control form-control-sm" formControlName="rowSpacing" min="20" max="100">
              </div>
              <div class="col-6">
                <label class="form-label">Seat Spacing</label>
                <input type="number" class="form-control form-control-sm" formControlName="seatSpacing" min="15" max="50">
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Row Naming</label>
              <select class="form-select form-select-sm" formControlName="rowNaming">
                <option [value]="RowNaming.ALPHABETIC">Alphabetic (A, B, C...)</option>
                <option [value]="RowNaming.NUMERIC">Numeric (1, 2, 3...)</option>
                <option [value]="RowNaming.ROMAN">Roman (I, II, III...)</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Curve</label>
              <input type="range" class="form-range" formControlName="curve" min="0" max="50">
              <small class="text-muted">{{ rowForm.get('curve')?.value }}°</small>
            </div>
            <button type="button" class="btn btn-success btn-sm w-100" (click)="createRows()">
              <i class="fas fa-plus me-1"></i>
              Add Rows
            </button>
          </form>
        </div>

      </div>
    </div>

    <!-- Canvas Area -->
    <div class="canvas-area">
      <div class="canvas-container" #canvasContainer></div>
      
      <!-- Canvas Overlay Info -->
      <div class="canvas-overlay">
        <div class="overlay-info">
          <span *ngIf="viewport" class="badge bg-dark">
            Zoom: {{ (viewport.scale * 100) | number:'1.0-0' }}%
          </span>
          <span *ngIf="toolConfig" class="badge bg-primary ms-2">
            Tool: {{ toolConfig.selectedTool | titlecase }}
          </span>
        </div>
      </div>
    </div>

    <!-- Right Sidebar - Statistics -->
    <div class="sidebar right-sidebar" *ngIf="showStats">
      <div class="sidebar-content">
        
        <!-- Venue Statistics -->
        <div class="property-panel">
          <h6 class="panel-title">
            <i class="fas fa-chart-bar me-2"></i>
            Venue Statistics
          </h6>
          <div class="stats-grid mt-3" *ngIf="stats">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalSeats | number }}</div>
              <div class="stat-label">Total Seats</div>
            </div>
            <div class="stat-item">
              <div class="stat-value text-success">{{ stats.availableSeats | number }}</div>
              <div class="stat-label">Available</div>
            </div>
            <div class="stat-item">
              <div class="stat-value text-info">{{ stats.selectedSeats | number }}</div>
              <div class="stat-label">Selected</div>
            </div>
            <div class="stat-item">
              <div class="stat-value text-danger">{{ stats.occupiedSeats | number }}</div>
              <div class="stat-label">Occupied</div>
            </div>
          </div>
        </div>

        <!-- Section Statistics -->
        <div class="property-panel" *ngIf="stats?.sectionStats && (stats?.sectionStats?.length || 0) > 0">
          <h6 class="panel-title">
            <i class="fas fa-list me-2"></i>
            Section Breakdown
          </h6>
          <div class="section-stats mt-3">
            <div class="section-stat" *ngFor="let section of stats?.sectionStats || []">
              <div class="section-name">{{ section.sectionName }}</div>
              <div class="section-numbers">
                <small class="text-muted">
                  {{ section.totalSeats }} seats
                  <span class="text-success">({{ section.availableSeats }} available)</span>
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Selection Info -->
        <div class="property-panel" *ngIf="selection && (selection.selectedSeats.length > 0 || selection.selectedSections.length > 0 || selection.selectedRows.length > 0)">
          <h6 class="panel-title">
            <i class="fas fa-check-square me-2"></i>
            Selection
          </h6>
          <div class="mt-3">
            <div *ngIf="selection.selectedSeats.length > 0" class="mb-2">
              <small class="text-muted">Seats:</small>
              <strong class="d-block">{{ selection.selectedSeats.length }}</strong>
            </div>
            <div *ngIf="selection.selectedSections.length > 0" class="mb-2">
              <small class="text-muted">Sections:</small>
              <strong class="d-block">{{ selection.selectedSections.length }}</strong>
            </div>
            <div *ngIf="selection.selectedRows.length > 0" class="mb-2">
              <small class="text-muted">Rows:</small>
              <strong class="d-block">{{ selection.selectedRows.length }}</strong>
            </div>

            <div class="btn-group w-100 mt-3" role="group">
              <button class="btn btn-outline-primary btn-sm" (click)="exportSelectedToJson()" title="Export Selected">
                <i class="fas fa-download"></i>
              </button>
              <button class="btn btn-outline-danger btn-sm" (click)="deleteSelected()" title="Delete Selected">
                <i class="fas fa-trash"></i>
              </button>
              <button class="btn btn-outline-secondary btn-sm" (click)="clearSelection()" title="Clear Selection">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="property-panel">
          <h6 class="panel-title">
            <i class="fas fa-keyboard me-2"></i>
            Shortcuts
          </h6>
          <div class="mt-3">
            <div class="shortcut-item">
              <kbd>Ctrl+Click</kbd>
              <span>Multi-select</span>
            </div>
            <div class="shortcut-item">
              <kbd>Right Click</kbd>
              <span>Context menu</span>
            </div>
            <div class="shortcut-item">
              <kbd>Delete</kbd>
              <span>Delete selected</span>
            </div>
            <div class="shortcut-item">
              <kbd>Ctrl+A</kbd>
              <span>Select all</span>
            </div>
            <div class="shortcut-item">
              <kbd>Escape</kbd>
              <span>Clear selection</span>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>
