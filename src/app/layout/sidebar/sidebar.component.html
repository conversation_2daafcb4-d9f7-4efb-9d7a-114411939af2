<nav
  class="sidebar bg-light"
  [ngClass]="{
    'open': isOpen,
    'pinned': isPinned,
    'expanded': isExpanded,
    'collapsed': !isPinned && !isExpanded && !isMobile
  }"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <!-- Hide the pin button on mobile devices -->
  <button class="btn btn-link pin-button" (click)="togglePin()" *ngIf="!isMobile">
    <span *ngIf="!isPinned">📌</span>
    <span *ngIf="isPinned">📍</span>
  </button>
  <ul class="nav flex-column">
    <li class="nav-item">
      <a
        class="nav-link"
        routerLink="/dashboard"
        (click)="onLinkClick()"
      >
        <span class="icon">🏠</span>
        <span class="text" *ngIf="isExpanded || isPinned || isOpen">Dashboard</span>
      </a>
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        routerLink="/test"
        routerLinkActive="active"
        (click)="onLinkClick()"
      >
        <span class="icon">🧪</span>
        <span class="text" *ngIf="isExpanded || isPinned || isOpen">Test Page</span>
      </a>
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        routerLink="/buildings/add"
        routerLinkActive="active"
        (click)="onLinkClick()"
      >
        <span class="icon">🏢</span>
        <span class="text" *ngIf="isExpanded || isPinned || isOpen">Προσθήκη Κτιρίου</span>
      </a>
    </li>
    <li class="nav-item">
      <a
        class="nav-link"
        routerLink="/venue-designer"
        routerLinkActive="active"
        (click)="onLinkClick()"
      >
        <span class="icon">🎭</span>
        <span class="text" *ngIf="isExpanded || isPinned || isOpen">Venue Designer</span>
      </a>
    </li>
    <!-- Add more navigation links with (click)="onLinkClick()" -->
  </ul>
</nav>
